// ----------------------------------------------------------------------

const ROOTS = {
  AUTH: '/auth',
  DASHBOARD: '/dashboard',
};

// ----------------------------------------------------------------------

export const paths = {
  comingSoon: '/coming-soon',
  page403: '/error/403',
  page404: '/error/404',
  page500: '/error/500',
  // AUTH
  auth: {
    jwt: {
      login: `${ROOTS.AUTH}/login`,
      verify: `${ROOTS.AUTH}/verify`,
      newPassword: `${ROOTS.AUTH}/reset-password`,
      forgotPassword: `${ROOTS.AUTH}/forgot-password`,
    },
  },
  // DASHBOARD
  dashboard: {
    root: '',
    tags: {
      root: `${ROOTS.DASHBOARD}/tags`,
    },
    general: {
      app: `${ROOTS.DASHBOARD}/app`,
    },
    account: {
      root: `${ROOTS.DASHBOARD}/accounts`,
    },

    company: {
      root: `${ROOTS.DASHBOARD}/companies`,
      new: `${ROOTS.DASHBOARD}/companies/new`,
      show: (companyId: string) => `${ROOTS.DASHBOARD}/companies/${companyId}/`,
      edit: (companyId: string) => `${ROOTS.DASHBOARD}/companies/${companyId}/update`,
    },

    warehouse: {
      root: (companyId: string) => `${ROOTS.DASHBOARD}/companies/${companyId}/warehouses`,
      new: (companyId: string) => `${ROOTS.DASHBOARD}/companies/${companyId}/warehouses/new`,
      show: (companyId: string, warehouseId: string) =>
        `${ROOTS.DASHBOARD}/companies/${companyId}/warehouses/${warehouseId}`,
      edit: (companyId: string, warehouseId: string) =>
        `${ROOTS.DASHBOARD}/companies/${companyId}/warehouses/${warehouseId}/update`,
    },

    product: {
      root: `${ROOTS.DASHBOARD}/products`,
      new: `${ROOTS.DASHBOARD}/products/new`,
      show: (warehouseId: string) => `${ROOTS.DASHBOARD}/products/${warehouseId}`,
      edit: (warehouseId: string) => `${ROOTS.DASHBOARD}/products/${warehouseId}/update`,
    },

    movers: {
      root: `${ROOTS.DASHBOARD}/movers`,
      new: `${ROOTS.DASHBOARD}/movers/new`,
      show: (moverId: string) => `${ROOTS.DASHBOARD}/movers/${moverId}`,
      edit: (moverId: string) => `${ROOTS.DASHBOARD}/movers/${moverId}/update`,
    },

    admin: {
      tenant: {
        root: `${ROOTS.DASHBOARD}/admin/tenants`,
        new: `${ROOTS.DASHBOARD}/admin/tenants/new`,
        show: (tenantId: string) => `${ROOTS.DASHBOARD}/admin/tenants/${tenantId}/`,
        edit: (tenantId: string) => `${ROOTS.DASHBOARD}/admin/tenants/${tenantId}/update`,
        integrations: {
          root: (tenantId: string) => `${ROOTS.DASHBOARD}/admin/tenants/${tenantId}/integrations`,
          magazord: {
            config: (tenantId: string, integrationId: string) =>
              `${ROOTS.DASHBOARD}/admin/tenants/${tenantId}/integrations/${integrationId}/magazord/config`,
          },
        },
      },
    },

    packaging: {
      root: `${ROOTS.DASHBOARD}/packaging`,
      new: `${ROOTS.DASHBOARD}/packaging/new`,
      show: (packagingId: string) => `${ROOTS.DASHBOARD}/packaging/${packagingId}/`,
      edit: (packagingId: string) => `${ROOTS.DASHBOARD}/packaging/${packagingId}/update`,
    },

    actions: {
      root: `${ROOTS.DASHBOARD}/actions`,
      move: {
        product: {
          root: `${ROOTS.DASHBOARD}/actions/move-product`,
        },
        mover: {
          root: `${ROOTS.DASHBOARD}/actions/move-mover`,
          replace: `${ROOTS.DASHBOARD}/actions/move-mover/replace`,
        },
      },
    },
  },
};
