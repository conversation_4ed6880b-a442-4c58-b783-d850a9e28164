const ROOT_PATHS = {
  root: `/v1`,
  auth: `/v1/auth`,
  states: `/v1/states`,
  tenant: `/v1/tenants`,
};

export const pathsBackend = {
  // AUTH
  auth: {
    login: `${ROOT_PATHS.auth}/login`,
    logout: `${ROOT_PATHS.auth}/logout`,
    myAccount: `${ROOT_PATHS.auth}/me`,
    refreshTokens: `${ROOT_PATHS.auth}/refresh-token`,
    resetPassword: `${ROOT_PATHS.auth}/reset-password`,
    changePassword: `${ROOT_PATHS.auth}/change-password`,
    forgotPassword: `${ROOT_PATHS.auth}/forgot-password`,
  },

  // STATES
  states: {
    getAll: ROOT_PATHS.states,
    show: (stateUf: string) => `${ROOT_PATHS.states}/${stateUf}`,
  },

  // CITIES
  cities: {
    getAll: (stateUf: string) => `${ROOT_PATHS.states}/${stateUf}/cities`,
    show: (stateUf: string, cityId: string) => `${ROOT_PATHS.states}/${stateUf}/cities/${cityId}`,
  },

  // TENANT
  tenant: {
    getAll: ROOT_PATHS.tenant,
    create: ROOT_PATHS.tenant,

    myTenant: `${ROOT_PATHS.tenant}/my-tenant`,

    show: (tenantId: string) => `${ROOT_PATHS.tenant}/${tenantId}`,
    getAllWarehouse: (tenantId: string) => `${ROOT_PATHS.tenant}/${tenantId}/warehouses`,
    update: (tenantId: string) => `${ROOT_PATHS.tenant}/${tenantId}`,
    delete: (tenantId: string) => `${ROOT_PATHS.tenant}/${tenantId}`,

    // ROLES
    roles: {
      getAll: () => `${ROOT_PATHS}/roles`,

      create: () => `${ROOT_PATHS}/roles`,

      show: (roleId: string) => `${ROOT_PATHS}/roles/${roleId}`,
      update: (roleId: string) => `${ROOT_PATHS}/roles/${roleId}`,
      delete: (roleId: string) => `${ROOT_PATHS}/roles/${roleId}`,
    },

    // INTEGRATION
    integration: {
      getAll: () => `${ROOT_PATHS.root}/integrations`,

      create: () => `${ROOT_PATHS.root}/integrations`,

      show: (integrationId: string) => `${ROOT_PATHS.root}/integrations/${integrationId}`,
      update: (integrationId: string) => `${ROOT_PATHS.root}/integrations/${integrationId}`,
      delete: (integrationId: string) => `${ROOT_PATHS.root}/integrations/${integrationId}`,
      sync: (integrationId: string) => `${ROOT_PATHS.root}/integrations/${integrationId}/sync`,
    },
  },

  // ACCOUNT
  account: {
    getAll: () => `${ROOT_PATHS.root}/accounts`,
    create: () => `${ROOT_PATHS.root}/accounts`,

    show: (accountId: string) => `${ROOT_PATHS.root}/accounts/${accountId}`,
    update: (accountId: string) => `${ROOT_PATHS.root}/accounts/${accountId}`,
    delete: (accountId: string) => `${ROOT_PATHS.root}/accounts/${accountId}`,

    // PERMISSION
    permission: {
      getAll: (accountId: string) => `${ROOT_PATHS.root}/accounts/${accountId}/permissions`,

      create: (accountId: string) => `${ROOT_PATHS.root}/accounts/${accountId}/permissions`,

      show: (accountId: string, permissionId: string) =>
        `${ROOT_PATHS.root}/accounts/${accountId}/permissions/${permissionId}`,
      update: (accountId: string, permissionId: string) =>
        `${ROOT_PATHS.root}/accounts/${accountId}/permissions/${permissionId}`,
      delete: (accountId: string, permissionId: string) =>
        `${ROOT_PATHS.root}/accounts/${accountId}/permissions/${permissionId}`,
    },
  },

  // COMPANY (FILIAIS)
  company: {
    getAll: () => `${ROOT_PATHS.root}/companies`,

    create: () => `${ROOT_PATHS.root}/companies`,

    show: (companyId: string) => `${ROOT_PATHS.root}/companies/${companyId}`,
    update: (companyId: string) => `${ROOT_PATHS.root}/companies/${companyId}`,
    delete: (companyId: string) => `${ROOT_PATHS.root}/companies/${companyId}`,
  },

  warehouse: {
    getAll: (companyId: string) => `${ROOT_PATHS.root}/companies/${companyId}/warehouses`,

    create: (companyId: string) => `${ROOT_PATHS.root}/companies/${companyId}/warehouses`,

    show: (companyId: string, warehouseId: string) =>
      `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}`,
    update: (companyId: string, warehouseId: string) =>
      `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}`,
    delete: (companyId: string, warehouseId: string) =>
      `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}`,

    mover: {
      create: (companyId: string, warehouseId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/movers`,
      getAll: (companyId: string, warehouseId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/movers`,
      update: (companyId: string, warehouseId: string, moverId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/movers/${moverId}`,
      delete: (companyId: string, warehouseId: string, moverId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/movers/${moverId}`,

      replaceMover: (companyId: string, warehouseId: string, moverId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/movers/${moverId}/replace`,
    },

    street: {
      getAll: (companyId: string, warehouseId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/streets`,

      create: (companyId: string, warehouseId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/streets`,

      show: (companyId: string, warehouseId: string, streetId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/streets/${streetId}`,
      update: (companyId: string, warehouseId: string, streetId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/streets/${streetId}`,
      delete: (companyId: string, warehouseId: string, streetId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/streets/${streetId}`,
    },
    module: {
      getAll: (companyId: string, warehouseId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/modules`,

      create: (companyId: string, warehouseId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/modules`,

      show: (companyId: string, warehouseId: string, moduleId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/modules/${moduleId}`,
      update: (companyId: string, warehouseId: string, moduleId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/modules/${moduleId}`,
      delete: (companyId: string, warehouseId: string, moduleId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/modules/${moduleId}`,
    },
    quadrant: {
      getAll: (companyId: string, warehouseId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/quadrants`,
      show: (companyId: string, warehouseId: string, quadrantId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/quadrants/${quadrantId}`,
      update: (companyId: string, warehouseId: string, quadrantId: string) =>
        `${ROOT_PATHS.root}/companies/${companyId}/warehouses/${warehouseId}/quadrants/${quadrantId}`,
    },
  },

  movement: {
    partialMoveProduct: () => `${ROOT_PATHS.root}/movements/partial`,
    totalMoveProduct: () => `${ROOT_PATHS.root}/movements/total`,
    allMoveProduct: () => `${ROOT_PATHS.root}/movements/all-items`,
  },

  product: {
    getAll: () => `${ROOT_PATHS.root}/products`,

    create: () => `${ROOT_PATHS.root}/products`,

    show: (productId: string) => `${ROOT_PATHS.root}/products/${productId}`,
    update: (productId: string) => `${ROOT_PATHS.root}/products/${productId}`,
    delete: (productId: string) => `${ROOT_PATHS.root}/products/${productId}`,
  },

  tag: {
    getAll: () => `${ROOT_PATHS.root}/tags`,
    show: (tagId: string) => `${ROOT_PATHS.root}/tags/${tagId}`,
  },

  packaging: {
    getAll: () => `${ROOT_PATHS.root}/packagings`,
    show: (packagingId: string) => `${ROOT_PATHS.root}/packagings/${packagingId}`,
    create: () => `${ROOT_PATHS.root}/packagings`,
    update: (packagingId: string) => `${ROOT_PATHS.root}/packagings/${packagingId}`,
    delete: (packagingId: string) => `${ROOT_PATHS.root}/packagings/${packagingId}`,
  },
  identifier: {
    show: (identifierId: string) => `${ROOT_PATHS.root}/identifiers/${identifierId}`,
  },
};
