// ----------------------------------------------------------------------

import { IAccount } from 'src/types/account';

export type ActionMapType<M extends { [index: string]: any }> = {
  [Key in keyof M]: M[Key] extends undefined
    ? {
        type: Key;
      }
    : {
        type: Key;
        payload: M[Key];
      };
};

export type AuthAccountType = null | Pick<
  IAccount,
  'id' | 'name' | 'email' | 'isSuper' | 'role' | 'tenantId'
>;

export type AuthStateType = {
  status?: string;
  loading: boolean;
  account: AuthAccountType;
};

// ----------------------------------------------------------------------

type CanRemove = {
  login?: (email: string, password: string) => Promise<void>;
  //
  confirmRegister?: (email: string, code: string) => Promise<void>;
  forgotPassword?: (email: string) => Promise<void>;
  resendCodeRegister?: (email: string) => Promise<void>;
  newPassword?: (email: string, code: string, password: string) => Promise<void>;
  updatePassword?: (password: string) => Promise<void>;
};

export type JWTContextType = CanRemove & {
  account: AuthAccountType;
  loading: boolean;
  authenticated: boolean;
  unauthenticated: boolean;
  logout: () => Promise<void>;
  login: (email: string, password: string) => Promise<void>;
};
