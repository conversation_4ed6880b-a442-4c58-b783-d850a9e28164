'use client';

import { m } from 'framer-motion';

import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import { Theme, SxProps } from '@mui/material/styles';

import { AccountRole } from 'src/enum/account';
import { ForbiddenIllustration } from 'src/assets/illustrations';

import { varBounce, MotionContainer } from 'src/components/animate';

import { useAuthContext } from '../hooks';

// ----------------------------------------------------------------------

type RoleBasedGuardProp = {
  isSuper?: boolean;
  hasContent?: boolean;
  roles?: AccountRole[];
  children: React.ReactNode;
  sx?: SxProps<Theme>;
};

export default function RoleBasedGuard({
  hasContent,
  isSuper,
  roles,
  children,
  sx,
}: RoleBasedGuardProp) {
  const { account } = useAuthContext();

  const currentRole = account?.role;

  const withContent = (
    <Container component={MotionContainer} sx={{ textAlign: 'center', ...sx }}>
      <m.div variants={varBounce().in}>
        <Typography variant="h3" sx={{ mb: 2 }}>
          Permissão Negada
        </Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <Typography sx={{ color: 'text.secondary' }}>
          Voce não tem permissão para acessar esta página
        </Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <ForbiddenIllustration
          sx={{
            height: 260,
            my: { xs: 5, sm: 10 },
          }}
        />
      </m.div>
    </Container>
  );

  if (isSuper && !account?.isSuper) {
    return hasContent ? withContent : null;
  }

  // TODO: TO BE REFACTORED LATER
  // if (Array.isArray(roles) && !roles.includes(currentRole ?? AccountRole.CLIENT)) {
  //   return hasContent ? withContent : null;
  // }

  return <> {children} </>;
}
