// eslint-disable-next-line import/no-cycle
import axiosInstance from 'src/utils/axios';

// eslint-disable-next-line import/no-cycle
import { ACCESS_KEY, REFRESH_KEY } from './auth-provider';

// ----------------------------------------------------------------------

function jwtDecode(token: string) {
  const base64Url = token.split('.')[1];
  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
  const jsonPayload = decodeURIComponent(
    window
      .atob(base64)
      .split('')
      .map((c) => `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`)
      .join('')
  );

  return JSON.parse(jsonPayload);
}

// ----------------------------------------------------------------------

export const isValidToken = (accessToken: string) => {
  if (!accessToken) {
    return false;
  }

  const decoded = jwtDecode(accessToken);

  const currentTime = Date.now() / 1000;

  return decoded.exp > currentTime;
};

// ----------------------------------------------------------------------

export const getAccessToken = () => {
  const accessToken = localStorage.getItem(ACCESS_KEY);

  return accessToken;
};

export const setAccessToken = (accessToken: string | null) => {
  if (accessToken) {
    localStorage.setItem(ACCESS_KEY, accessToken);

    axiosInstance.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
  } else {
    localStorage.removeItem(ACCESS_KEY);

    delete axiosInstance.defaults.headers.common.Authorization;
  }
};

// ----------------------------------------------------------------------

export const getRefreshToken = () => {
  const refreshToken = localStorage.getItem(REFRESH_KEY);

  return refreshToken;
};

export const setRefreshToken = (refreshToken: string | null) => {
  if (refreshToken) {
    localStorage.setItem(REFRESH_KEY, refreshToken);
  } else {
    localStorage.removeItem(REFRESH_KEY);
  }
};
