'use client';

import { isEqual } from 'lodash';
import { useMemo, useEffect, useReducer, useCallback } from 'react';

// eslint-disable-next-line import/no-cycle
import { useMyAccountQuery } from 'src/queries/auth';
// eslint-disable-next-line import/no-cycle
import { useLogin, useLogout } from 'src/mutations/auth';
// eslint-disable-next-line import/no-cycle

import { AuthContext } from './auth-context';
import { ActionMapType, AuthStateType, AuthAccountType } from '../../types';
import { isValidToken, getAccessToken, setAccessToken, setRefreshToken } from './utils';

// ----------------------------------------------------------------------
/**
 * NOTE:
 * We only build demo at basic level.
 * Customer will need to do some extra handling yourself if you want to extend the logic and other features...
 */
// ----------------------------------------------------------------------

enum Types {
  INITIAL = 'INITIAL',
  LOGIN = 'LOGIN',
  REGISTER = 'REGISTER',
  LOGOUT = 'LOGOUT',
}

type Payload = {
  [Types.INITIAL]: {
    account: AuthAccountType;
  };
  [Types.LOGIN]: {
    account: AuthAccountType;
  };
  [Types.LOGOUT]: undefined;
};

type ActionsType = ActionMapType<Payload>[keyof ActionMapType<Payload>];

// ----------------------------------------------------------------------

const initialState: AuthStateType = {
  account: null,
  loading: true,
};

const reducer = (state: AuthStateType, action: ActionsType) => {
  if (action.type === Types.INITIAL) {
    return {
      loading: false,
      account: action.payload.account,
    };
  }
  if (action.type === Types.LOGIN) {
    return {
      ...state,
      account: action.payload.account,
    };
  }
  if (action.type === Types.LOGOUT) {
    return {
      ...state,
      account: null,
    };
  }
  return state;
};

// ----------------------------------------------------------------------

export const TENANT_KEY = 'tenantId';
export const ACCESS_KEY = 'accessToken';
export const REFRESH_KEY = 'refreshToken';

type Props = {
  children: React.ReactNode;
};

export function AuthProvider({ children }: Props) {
  const loginMutation = useLogin();

  const useLogoutMutation = useLogout();

  const [state, dispatch] = useReducer(reducer, initialState);

  const { data: me, refetch: refetchMyAccount } = useMyAccountQuery();

  useEffect(() => {
    // prevent many re-renders, and only set when values are discrepant
    if (me && !isEqual(me?.account, state.account)) {
      dispatch({
        type: Types.INITIAL,
        payload: {
          account: me.account,
        },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [me]);

  const initialize = useCallback(async () => {
    try {
      const accessToken = getAccessToken();

      if (accessToken && isValidToken(accessToken)) {
        // THESE FUNCTION MUST BE CALLED TO SET ACCESS TOKEN AGAIN IN HEADERS OF AXIOS INSTANCE
        setAccessToken(accessToken);

        const { data: myAccount } = await refetchMyAccount();

        const account = myAccount?.account;

        if (account) {
          dispatch({
            type: Types.INITIAL,
            payload: {
              account: {
                ...account,
              },
            },
          });
        } else {
          dispatch({
            type: Types.INITIAL,
            payload: {
              account: null,
            },
          });
        }
      } else {
        dispatch({
          type: Types.INITIAL,
          payload: {
            account: null,
          },
        });
      }
    } catch (error) {
      console.error(error);
      dispatch({
        type: Types.INITIAL,
        payload: {
          account: null,
        },
      });
    }
  }, [refetchMyAccount]);

  useEffect(() => {
    initialize();
  }, [initialize]);

  // LOGIN
  const login = useCallback(
    async (email: string, password: string) => {
      const data = {
        email,
        password,
      };

      const res = await loginMutation.mutateAsync(data);

      const {
        tokens: { accessToken, refreshToken },
        account,
      } = res;

      setAccessToken(accessToken);
      setRefreshToken(refreshToken);

      dispatch({
        type: Types.LOGIN,
        payload: {
          account: {
            ...account,
          },
        },
      });
    },
    [loginMutation]
  );

  // LOGOUT
  const logout = useCallback(async () => {
    try {
      const refreshToken = localStorage.getItem(REFRESH_KEY);

      if (refreshToken && isValidToken(refreshToken)) {
        await useLogoutMutation.mutateAsync({ refreshToken });

        setAccessToken(null);
        setRefreshToken(null);
      }
    } catch (error) {
      // TODO: THIS CATCH LOGIC NEED REVIEW MAYBE

      setAccessToken(null);
      setRefreshToken(null);
    } finally {
      dispatch({
        type: Types.LOGOUT,
      });
    }
  }, [useLogoutMutation]);

  // ----------------------------------------------------------------------

  const checkAuthenticated = state.account ? 'authenticated' : 'unauthenticated';

  const status = state.loading ? 'loading' : checkAuthenticated;

  const memoizedValue = useMemo(
    () => ({
      account: state.account,
      loading: status === 'loading',
      authenticated: status === 'authenticated',
      unauthenticated: status === 'unauthenticated',
      //
      login,
      logout,
    }),
    [login, logout, state.account, status]
  );

  return <AuthContext.Provider value={memoizedValue}>{children}</AuthContext.Provider>;
}
