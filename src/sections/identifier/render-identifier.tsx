import QRCode from 'qrcode.react';
import Barcode from 'react-barcode';

import { IdentifierValueType } from 'src/enum/identifier';

type RenderIdentifierProps = {
  value: string;
  valueType: IdentifierValueType;
};

export default function RenderIdentifier({ value, valueType }: RenderIdentifierProps) {
  switch (valueType) {
    case IdentifierValueType.EAN13:
      return <Barcode value={value} />;

    case IdentifierValueType.QRCODE:
      return (
        <QRCode
          size={180}
          value={value}
          renderAs="svg"
          style={{ height: 'auto', maxWidth: '100%', width: '100%' }}
        />
      );

    default:
      return <>Identificador inválido</>;
  }
}
