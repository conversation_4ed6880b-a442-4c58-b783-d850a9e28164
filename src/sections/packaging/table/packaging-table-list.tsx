import { isEqual } from 'lodash';
import { useState, useEffect, useCallback } from 'react';

import { Card, Table, TableBody, TableContainer } from '@mui/material';

import { usePackagingListSuspenseQuery } from 'src/queries/packaging';
import { usePackagingDeleteMutation } from 'src/mutations/packaging/packaging-mutation';

import Scrollbar from 'src/components/scrollbar';
import {
  useTable,
  TableNoData,
  getComparator,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

import { IPackaging } from 'src/types/packaging';
import { ITenantTableFilterValue } from 'src/types/tenant';

import PackagingTableRow from './packaging-table-row';
import PackagingTableToolbar from './packaging-table-toolbar';
import PackagingTableFiltersResult from './packaging-table-filters-result';

const TABLE_HEAD = [
  { id: 'id', label: 'Código' },
  { id: 'name', label: 'Nome' },
  { id: 'width', label: 'Largura' },
  { id: 'length', label: 'Comprimento' },
  { id: 'height', label: 'Altura' },
  { id: '', width: 88 },
];

const defaultFilters = {
  name: '',
};

export default function PackagingTableList() {
  const table = useTable();

  const [filters, setFilters] = useState(defaultFilters);

  const {
    data: { metadata, packagings },
    isFetching,
    isRefetching,
  } = usePackagingListSuspenseQuery({
    params: { page: table.page + 1, limit: table.rowsPerPage },
  });

  const { mutateAsync } = usePackagingDeleteMutation();

  const handleDeletePackaging = async (packagingId: string) => {
    mutateAsync({ packagingId });
  };

  const [tableData, setTableData] = useState<IPackaging[]>(packagings ?? []);

  const dataFiltered = applyFilter({
    inputData: tableData,
    comparator: getComparator(table.order, table.orderBy),
    filters,
  });

  const canReset = !isEqual(defaultFilters, filters);

  const notFound = !isFetching && !isRefetching && !tableData.length;

  const handleFilters = useCallback(
    (name: string, value: ITenantTableFilterValue) => {
      table.onResetPage();
      setFilters((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    },
    [table]
  );

  const handleResetFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);

  useEffect(() => {
    setTableData(packagings);
  }, [packagings]);

  return (
    <Card>
      <PackagingTableToolbar filters={filters} onFilters={handleFilters} />

      {canReset && (
        <PackagingTableFiltersResult
          filters={filters}
          onFilters={handleFilters}
          //
          onResetFilters={handleResetFilters}
          //
          results={dataFiltered.length}
          sx={{ p: 2.5, pt: 0 }}
        />
      )}
      <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
        <Scrollbar>
          <Table size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
            <TableHeadCustom
              order={table.order}
              orderBy={table.orderBy}
              headLabel={TABLE_HEAD}
              onSort={table.onSort}
            />
            <TableBody>
              {dataFiltered.map((row) => (
                <PackagingTableRow
                  key={row.id}
                  row={row}
                  onDeleteRow={() => handleDeletePackaging(row.id)}
                />
              ))}

              <TableNoData notFound={notFound} />
            </TableBody>
          </Table>
        </Scrollbar>
      </TableContainer>

      <TablePaginationCustom
        totalPages={Math.floor(metadata.total / table.rowsPerPage)}
        count={metadata.total}
        page={table.page}
        rowsPerPage={table.rowsPerPage}
        onPageChange={table.onChangePage}
        onRowsPerPageChange={table.onChangeRowsPerPage}
        //
        dense={table.dense}
        onChangeDense={table.onChangeDense}
      />
    </Card>
  );
}

function applyFilter({
  inputData,
  comparator,
  filters,
}: {
  inputData: IPackaging[];
  comparator: (a: any, b: any) => number;
  filters: typeof defaultFilters;
}) {
  const { name } = filters;

  const stabilizedThis = inputData?.map((el, index) => [el, index] as const);

  stabilizedThis?.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });

  inputData = stabilizedThis.map((el) => el[0]);

  if (name) {
    inputData = inputData.filter(
      (company) => company.name.toLowerCase().indexOf(name.toLowerCase()) !== -1
    );
  }

  return inputData;
}
