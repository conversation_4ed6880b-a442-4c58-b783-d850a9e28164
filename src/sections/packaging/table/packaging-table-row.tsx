import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, TableRow, Table<PERSON>ell, IconButton } from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';

import Iconify from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';

import { IPackaging } from 'src/types/packaging';

import PackagingQuickEditForm from '../view/packaging-quick-edit-form';

type Props = {
  row: IPackaging;
  onDeleteRow: VoidFunction;
};

export default function PackagingTableRow({ row, onDeleteRow }: Props) {
  const { id, name, width, length, height } = row;

  const edit = useBoolean();
  const confirm = useBoolean();

  return (
    <>
      <TableRow hover>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>{id}</TableCell>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>{name}</TableCell>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>{width}</TableCell>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>{length}</TableCell>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>{height}</TableCell>

        <TableCell align="right" sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title="Editar Integração" placement="top" arrow>
            <IconButton color="default" onClick={edit.onTrue}>
              <Iconify icon="solar:pen-bold" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Excluir Integração" placement="top" arrow>
            <IconButton color="error" onClick={confirm.onTrue}>
              <Iconify icon="solar:trash-bin-trash-bold" />
            </IconButton>
          </Tooltip>
        </TableCell>
      </TableRow>

      <ConfirmDialog
        open={confirm.value}
        onClose={confirm.onFalse}
        title="Excluir"
        content="Tem certeza que deseja excluir esta empresa? Essa ação não poderá ser desfeita."
        action={
          <Button variant="contained" color="error" onClick={onDeleteRow}>
            Excluir
          </Button>
        }
      />
      {edit.value && (
        <PackagingQuickEditForm onClose={edit.onFalse} open={edit.value} currentPackaging={row} />
      )}
    </>
  );
}
