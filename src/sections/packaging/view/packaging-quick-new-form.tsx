import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import Dialog, { DialogProps } from '@mui/material/Dialog';

import { CreatePackagingSchema } from 'src/schemas/packaging';
import { usePackagingCreateMutation } from 'src/mutations/packaging/packaging-mutation';

import FormProvider, { RHFTextField } from 'src/components/hook-form';

import { IPackagingNewForm } from 'src/types/packaging';

// ----------------------------------------------------------------------

type Props = {
  open: boolean;
  onClose: VoidFunction;
} & DialogProps;

export default function PackagingQuickNewForm({ open, onClose, ...rest }: Props) {
  const defaultValues: IPackagingNewForm = useMemo(
    () => ({
      width: 0,
      height: 0,
      length: 0,
    }),
    []
  );

  const methods = useForm<IPackagingNewForm>({
    resolver: yupResolver<IPackagingNewForm>(CreatePackagingSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const { mutateAsync, isPending } = usePackagingCreateMutation({
    callbackFn: () => {
      reset();
      onClose();
    },
  });

  const onSubmit = handleSubmit(async (newPackaging) => {
    mutateAsync({ newPackaging });
  });

  return (
    <Dialog
      fullWidth
      maxWidth={false}
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { maxWidth: 720 },
      }}
      {...rest}
    >
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <DialogTitle>Criar usuário</DialogTitle>

        <DialogContent>
          <Box
            rowGap={3}
            columnGap={2}
            paddingTop={1}
            display="grid"
            gridTemplateColumns={{
              xs: 'repeat(1, 1fr)',
              sm: 'repeat(2, 1fr)',
            }}
          >
            <RHFTextField name="width" label="Largura" placeholder="0" />
            <RHFTextField name="length" label="Comprimento" placeholder="0" />
            <RHFTextField name="height" label="Altura" placeholder="0" />
          </Box>
        </DialogContent>

        <DialogActions>
          <Button variant="outlined" onClick={onClose}>
            Cancelar
          </Button>

          <LoadingButton type="submit" variant="contained" loading={isSubmitting || isPending}>
            Criar
          </LoadingButton>
        </DialogActions>
      </FormProvider>
    </Dialog>
  );
}
