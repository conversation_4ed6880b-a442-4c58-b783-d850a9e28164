'use client';

import { Button } from '@mui/material';
import { Container } from '@mui/system';

import { paths } from 'src/routes/paths';

import { useBoolean } from 'src/hooks/use-boolean';

import Iconify from 'src/components/iconify';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';

import PackagingTableList from '../table/packaging-table-list';
import PackagingQuickNewForm from './packaging-quick-new-form';

// ----------------------------------------------------------------------

export default function PackagingListView() {
  const newPackaging = useBoolean();

  return (
    <>
      <Container maxWidth={false}>
        <CustomBreadcrumbs
          heading="Lista de Embalagens"
          links={[{ name: 'Início', href: paths.dashboard.root }, { name: 'Lista' }]}
          action={
            <Button
              onClick={newPackaging.onTrue}
              variant="contained"
              startIcon={<Iconify icon="mingcute:add-line" />}
            >
              Adicionar Embalagem
            </Button>
          }
          sx={{
            mb: { xs: 3, md: 5 },
          }}
        />
        <PackagingTableList />
      </Container>

      <PackagingQuickNewForm open={newPackaging.value} onClose={newPackaging.onFalse} />
    </>
  );
}
