import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { UpdatePackagingSchema } from 'src/schemas/packaging';
import { usePackagingUpdateMutation } from 'src/mutations/packaging/packaging-mutation';

import FormProvider, { RHFTextField } from 'src/components/hook-form';

import { IPackaging } from 'src/types/packaging';

// ----------------------------------------------------------------------

type Props = {
  open: boolean;
  onClose: VoidFunction;
  currentPackaging: IPackaging;
};

export default function PackagingQuickEditForm({
  currentPackaging,
  open,
  onClose,
}: Props) {
  const defaultValues = useMemo(
    () => ({
      name: currentPackaging?.name || '',
      width: currentPackaging?.width || 0,
      height: currentPackaging?.height || 0,
      length: currentPackaging?.length || 0,
    }),
    [currentPackaging]
  );

  const methods = useForm({
    resolver: yupResolver(UpdatePackagingSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const { mutateAsync, isPending } = usePackagingUpdateMutation({
    callbackFn: () => {
      reset();
      onClose();
    },
  });

  const onSubmit = handleSubmit(async (updatedPackaging) => {
    mutateAsync({ packagingId: currentPackaging.id, updatedPackaging });
  });

  return (
    <Dialog
      fullWidth
      maxWidth={false}
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { maxWidth: 720 },
      }}
    >
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <DialogTitle>Alterar usuário</DialogTitle>

        <DialogContent>
          <Box
            rowGap={2}
            columnGap={2}
            paddingTop={1}
            display="grid"
            gridTemplateColumns={{
              xs: 'repeat(1, 1fr)',
              sm: 'repeat(2, 1fr)',
            }}
          >
            <RHFTextField name="width" label="Largura" placeholder="0" />
            <RHFTextField name="length" label="Comprimento" placeholder="0" />
            <RHFTextField name="height" label="Altura" placeholder="0" />
          </Box>
        </DialogContent>

        <DialogActions>
          <Button variant="outlined" onClick={onClose}>
            Cancelar
          </Button>

          <LoadingButton type="submit" variant="contained" loading={isSubmitting || isPending}>
            Alterar
          </LoadingButton>
        </DialogActions>
      </FormProvider>
    </Dialog>
  );
}
