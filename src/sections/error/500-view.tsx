'use client';

import { m } from 'framer-motion';
import { useRouter } from 'next/navigation';

import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';

import CompactLayout from 'src/layouts/compact';
import { SeverErrorIllustration } from 'src/assets/illustrations';

import { varBounce, MotionContainer } from 'src/components/animate';

// ----------------------------------------------------------------------

export default function Page500() {
  const { back } = useRouter();

  return (
    <CompactLayout>
      <MotionContainer>
        <m.div variants={varBounce().in}>
          <Typography variant="h3" sx={{ mb: 2 }}>
            Ops, algo inesperado aconteceu.
          </Typography>
        </m.div>

        <m.div variants={varBounce().in}>
          <Typography sx={{ color: 'text.secondary' }}>
            Houve um erro, por favor, tente novamente mais tarde.
          </Typography>
        </m.div>

        <m.div variants={varBounce().in}>
          <SeverErrorIllustration sx={{ height: 260, my: { xs: 5, sm: 10 } }} />
        </m.div>

        <Button onClick={back} size="large" variant="contained">
          Voltar para a página anterior
        </Button>
      </MotionContainer>
    </CompactLayout>
  );
}
