'use client';

import { m } from 'framer-motion';
import { useRouter } from 'next/navigation';

import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';

import CompactLayout from 'src/layouts/compact';
import { PageNotFoundIllustration } from 'src/assets/illustrations';

import { varBounce, MotionContainer } from 'src/components/animate';

// ----------------------------------------------------------------------

export default function NotFoundView() {
  const { back } = useRouter();

  return (
    <CompactLayout>
      <MotionContainer>
        <m.div variants={varBounce().in}>
          <Typography variant="h3" sx={{ mb: 2 }}>
            Desculpa, página não encontrada!
          </Typography>
        </m.div>

        <m.div variants={varBounce().in}>
          <Typography sx={{ color: 'text.secondary' }}>
            <PERSON><PERSON><PERSON><PERSON>, não conseguimos encontrar a página que você está procurando. Talvez você tenha
            digitado o URL incorretamente? Certifique-se de verificar a sua ortografia.
          </Typography>
        </m.div>

        <m.div variants={varBounce().in}>
          <PageNotFoundIllustration
            sx={{
              height: 260,
              my: { xs: 5, sm: 10 },
            }}
          />
        </m.div>

        <Button onClick={back} size="large" variant="contained">
          Voltar para a página anterior
        </Button>
      </MotionContainer>
    </CompactLayout>
  );
}
