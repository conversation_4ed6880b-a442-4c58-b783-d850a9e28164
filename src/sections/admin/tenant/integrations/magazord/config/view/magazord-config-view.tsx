'use client';

import { Suspense } from 'react';
import { useParams } from 'next/navigation';

import Button from '@mui/material/Button';
// @mui
import Container from '@mui/material/Container';

// routes
import { paths } from 'src/routes/paths';

import MagazordIcon from 'src/assets/icons/magazord-icon';
import { useIntegregrationSyncMutation } from 'src/mutations/tenant/integration-mutation';

// components
import { useSettingsContext } from 'src/components/settings';
import { LoadingScreen } from 'src/components/loading-screen';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';

import MagazordShopsTable from '../table/magazord-shops-table';

// ----------------------------------------------------------------------

export default function MagazordConfigView() {
  const settings = useSettingsContext();

  const { tenantId, integrationId } = useParams<{ tenantId: string; integrationId: string }>();

  const syncIntegration = useIntegregrationSyncMutation();

  const handleSync = async () => {
    await syncIntegration.mutateAsync({
      integrationId,
    });
  };

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <CustomBreadcrumbs
        heading="Lojas magazord"
        links={[
          { name: 'Início', href: paths.dashboard.root },
          {
            name: 'Integrações',
            href: paths.dashboard.admin.tenant.integrations.root(tenantId),
          },
          { name: 'Lojas magazord' },
        ]}
        sx={{
          mb: { xs: 3, md: 5 },
        }}
        action={
          <Button
            variant="contained"
            onClick={handleSync}
            startIcon={<MagazordIcon fixedViewBox width={24} height={24} />}
          >
            Sincronizar lojas
          </Button>
        }
      />

      <Suspense fallback={<LoadingScreen />}>
        <MagazordShopsTable />
      </Suspense>
    </Container>
  );
}
