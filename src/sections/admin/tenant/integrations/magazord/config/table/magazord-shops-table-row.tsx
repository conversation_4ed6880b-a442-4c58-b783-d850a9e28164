import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import LoadingButton from '@mui/lab/LoadingButton';
import { Tooltip, IconButton } from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';

import { fDateTime } from 'src/utils/format-time';

import { useMagazordShopActiveMutation } from 'src/mutations/magazord/shop-mutation';

import Iconify from 'src/components/iconify';

import { IMagazordShop } from 'src/types/integrations/magazord/shop';

import ShopQuickEditForm from './shop-quick-edit-form';

interface MagazordShopsTableRowProps {
  row: IMagazordShop;
}

export default function MagazordShopsTableRow({ row }: MagazordShopsTableRowProps) {
  const useMagazordShopActive = useMagazordShopActiveMutation();

  const quickEdit = useBoolean();

  const handleActive = async () => {
    await useMagazordShopActive.mutateAsync({ shopId: row.id, active: !row.active });
  };

  return (
    <>
      <TableRow hover>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>{row.id}</TableCell>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>{row.rCodigo}</TableCell>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>{row.rNome}</TableCell>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>{row.active ? 'Sim' : 'Não'}</TableCell>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>{fDateTime(row.createdAt)}</TableCell>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>{fDateTime(row.updatedAt)}</TableCell>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>
          <LoadingButton variant="outlined" onClick={handleActive}>
            {row.active ? 'Desativar loja' : 'Ativar loja'}
          </LoadingButton>
        </TableCell>

        <TableCell align="right" sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title="Editar" placement="top" arrow>
            <IconButton color={quickEdit.value ? 'inherit' : 'default'} onClick={quickEdit.onTrue}>
              <Iconify icon="solar:pen-bold" />
            </IconButton>
          </Tooltip>
        </TableCell>
      </TableRow>

      <ShopQuickEditForm currentShop={row} open={quickEdit.value} onClose={quickEdit.onFalse} />
    </>
  );
}
