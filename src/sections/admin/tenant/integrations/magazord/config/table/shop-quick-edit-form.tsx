import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import { InputLabel } from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { useAuthContext } from 'src/auth/hooks';
import { UpdateMagazordShopSchema } from 'src/schemas/magazord-shop';
import { useTenantListWarehouseQuery } from 'src/queries/tenant/tenant-query';
import { MagazordShopSettingsType } from 'src/enum/magazord-shop-settings-type';
import { useMagazordShopUpdateMutation } from 'src/mutations/magazord/shop-mutation';

import FormProvider, { RHFSelect } from 'src/components/hook-form';

import { IMagazordShop, IMagazordShopEditForm } from 'src/types/integrations/magazord/shop';

// ----------------------------------------------------------------------

type Props = {
  open: boolean;
  onClose: VoidFunction;
  currentShop: IMagazordShop;
};

export default function ShopQuickEditForm({ currentShop, open, onClose }: Props) {
  const { account } = useAuthContext();
  const tenantId = account?.tenantId!;

  const { data: warehouses } = useTenantListWarehouseQuery(tenantId);

  const defaultValues: IMagazordShopEditForm = useMemo(
    () => ({
      status: currentShop?.status || 4,
      settings: {
        type: currentShop?.settings?.type || '',
        payload: {
          warehouseId: currentShop?.settings?.payload?.warehouseId || '',
        },
      },
    }),
    [currentShop]
  );

  const methods = useForm({
    resolver: yupResolver(UpdateMagazordShopSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    watch,
    formState: { isSubmitting },
  } = methods;

  const { mutateAsync, isPending } = useMagazordShopUpdateMutation({
    callbackFn: () => {
      reset();
      onClose();
    },
  });

  const onSubmit = handleSubmit(async (updatedShop) => {
    mutateAsync({
      shopId: currentShop.id,
      updatedShop: {
        ...updatedShop,
        status: Number(updatedShop.status),
        settings: {
          type: updatedShop.settings.type,
          payload: {
            warehouseId: String(updatedShop.settings.payload.warehouseId),
          },
        },
      },
    });
  });

  const selectedType = watch('settings.type');

  return (
    <Dialog
      fullWidth
      maxWidth={false}
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { maxWidth: 720 },
      }}
    >
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <DialogTitle>Configurar Loja</DialogTitle>

        <DialogContent>
          <Box
            rowGap={2}
            columnGap={2}
            paddingTop={1}
            display="grid"
            gridTemplateColumns={{
              xs: 'repeat(1, 1fr)',
              sm: 'repeat(2, 1fr)',
            }}
          >
            <RHFSelect name="status" label="Configuração de Status">
              <InputLabel id="status">Status</InputLabel>
              <MenuItem value={4}>Aprovado</MenuItem>
              <MenuItem value={5}>Aprovado e Integrado</MenuItem>
            </RHFSelect>

            <RHFSelect name="settings.type" label="Configuração de Armazém">
              <MenuItem value={MagazordShopSettingsType.SHOP_WAREHOUSE}>Loja e Armazém</MenuItem>
            </RHFSelect>

            {selectedType === MagazordShopSettingsType.SHOP_WAREHOUSE && (
              <RHFSelect name="settings.payload.warehouseId" label="Armazém">
                {warehouses?.map(({ companyName, warehouses: companyWarehouses }) => [
                  <InputLabel
                    key={`${companyName}-header`}
                    sx={{ color: 'text.secondary', fontStyle: 'italic', padding: 0.5 }}
                  >
                    {companyName}
                  </InputLabel>,
                  companyWarehouses.map((warehouse) => (
                    <MenuItem
                      key={warehouse.id}
                      value={warehouse.id}
                      sx={{ display: 'flex', alignItems: 'center' }}
                    >
                      <Box sx={{ minWidth: 160 }}>{warehouse.name}</Box>
                    </MenuItem>
                  )),
                ])}
              </RHFSelect>
            )}
          </Box>
        </DialogContent>

        <DialogActions>
          <Button variant="outlined" onClick={onClose}>
            Cancelar
          </Button>

          <LoadingButton type="submit" variant="contained" loading={isSubmitting || isPending}>
            Alterar
          </LoadingButton>
        </DialogActions>
      </FormProvider>
    </Dialog>
  );
}
