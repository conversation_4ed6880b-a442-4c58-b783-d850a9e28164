import { isEqual } from 'lodash';
import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';

import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';

import { useMagazordShopSuspenseListQuery } from 'src/queries/magazord/shop-query';

import Scrollbar from 'src/components/scrollbar';
import {
  useTable,
  TableNoData,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

import { IMagazordShop } from 'src/types/integrations/magazord/shop';

import MagazordShopsTableRow from './magazord-shops-table-row';

const TABLE_HEAD = [
  { id: 'id', label: 'ID interno', align: 'left' },
  { id: 'rCodigo', label: 'Código Magazord', align: 'left' },
  { id: 'rNome', label: 'Nome', align: 'left' },
  { id: 'active', label: 'Ativa', align: 'left' },
  { id: 'createdAt', label: 'Criada em', align: 'left' },
  { id: 'updatedAt', label: 'Atualizado em', align: 'left' },
  { id: '', label: '', align: 'left' },
  { id: 'actions', label: 'Ações', align: 'right' },
];

export default function MagazordShopsTable() {
  const { tenantId, integrationId } = useParams<{ tenantId: string; integrationId: string }>();

  const {
    dense,
    page,
    rowsPerPage,
    order,
    orderBy,
    // methods
    onChangeDense,
    onChangePage,
    onChangeRowsPerPage,
  } = useTable();

  const [tableData, setTableData] = useState<IMagazordShop[]>([]);

  const { data: magazordShops } = useMagazordShopSuspenseListQuery({
    tenantId,
    integrationId,
  });

  useEffect(() => {
    if (!isEqual(tableData, magazordShops)) {
      setTableData(magazordShops);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [magazordShops]);

  const isNotFound = !tableData.length;

  return (
    <Card>
      <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
        <Scrollbar>
          <Table size={dense ? 'small' : 'medium'} sx={{ minWidth: 800 }}>
            <TableHeadCustom order={order} orderBy={orderBy} headLabel={TABLE_HEAD} />

            <TableBody>
              <>
                {tableData.map((row) => (
                  <MagazordShopsTableRow key={row.id} row={row} />
                ))}

                <TableNoData notFound={isNotFound} />
              </>
            </TableBody>
          </Table>
        </Scrollbar>
      </TableContainer>

      <TablePaginationCustom
        page={page}
        totalPages={tableData.length}
        rowsPerPage={rowsPerPage}
        onPageChange={onChangePage}
        onRowsPerPageChange={onChangeRowsPerPage}
        count={tableData?.length ?? 0}
        // dense
        dense={dense}
        onChangeDense={onChangeDense}
      />
    </Card>
  );
}
