// @mui
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import IconButton from '@mui/material/IconButton';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

// hooks
import { useBoolean } from 'src/hooks/use-boolean';

import { fDateTime } from 'src/utils/format-time';
import { fMaskPhone } from 'src/utils/format-phone';

// components
import Iconify from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';

//
import { ITenant } from 'src/types/tenant';

// ----------------------------------------------------------------------

type Props = {
  row: ITenant;
  onDeleteRow: VoidFunction;
};

export default function TenantTableRow({ row, onDeleteRow }: Props) {
  const { id, name, email, phone, createdAt, updatedAt } = row;

  const confirm = useBoolean();

  return (
    <>
      <TableRow hover>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>{name}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{email}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{fMaskPhone(phone)}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{fDateTime(createdAt)}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{fDateTime(updatedAt)}</TableCell>

        <TableCell align="right" sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title="Ver Integrações" placement="top" arrow>
            <IconButton
              color="success"
              component={RouterLink}
              href={paths.dashboard.admin.tenant.integrations.root(id)}
            >
              <Iconify icon="mdi:puzzle" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Editar Empresa" placement="top" arrow>
            <IconButton
              color="warning"
              component={RouterLink}
              href={paths.dashboard.admin.tenant.edit(id)}
            >
              <Iconify icon="solar:pen-bold" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Excluir Empresa" placement="top" arrow>
            <IconButton color="error" onClick={confirm.onTrue}>
              <Iconify icon="solar:trash-bin-trash-bold" />
            </IconButton>
          </Tooltip>
        </TableCell>
      </TableRow>

      <ConfirmDialog
        open={confirm.value}
        onClose={confirm.onFalse}
        title="Excluir"
        content="Tem certeza que deseja excluir esta empresa? Essa ação não poderá ser desfeita."
        action={
          <Button variant="contained" color="error" onClick={onDeleteRow}>
            Excluir
          </Button>
        }
      />
    </>
  );
}
