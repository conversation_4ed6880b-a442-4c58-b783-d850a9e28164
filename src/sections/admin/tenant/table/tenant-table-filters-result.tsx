// @mui
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Stack, { StackProps } from '@mui/material/Stack';

// components
import Iconify from 'src/components/iconify';

import { ITenantTableFilters, ITenantTableFilterValue } from 'src/types/tenant';

// ----------------------------------------------------------------------

type Props = StackProps & {
  filters: ITenantTableFilters;
  onFilters: (name: string, value: ITenantTableFilterValue) => void;
  //
  onResetFilters: VoidFunction;
  //
  results: number;
};

export default function TenantTableFiltersResult({
  filters,
  onFilters,
  //
  onResetFilters,
  //
  results,
  ...other
}: Props) {
  return (
    <Stack spacing={1.5} flex={1} flexDirection="row" alignItems="center" {...other}>
      <strong>{results}</strong>
      <Box component="span" sx={{ color: 'text.secondary', ml: 0.25 }}>
        resultados encontrados
      </Box>
      <Button
        color="error"
        onClick={onResetFilters}
        startIcon={<Iconify icon="solar:trash-bin-trash-bold" />}
      >
        Limpar
      </Button>
    </Stack>
  );
}
