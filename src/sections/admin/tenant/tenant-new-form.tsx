import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { yupResolver } from '@hookform/resolvers/yup';

// @mui
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Unstable_Grid2';
import Typography from '@mui/material/Typography';
import CardHeader from '@mui/material/CardHeader';
import LoadingButton from '@mui/lab/LoadingButton';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { useResponsive } from 'src/hooks/use-responsive';

import { NewTenantSchema } from 'src/schemas/admin/tenant';
import { useTenantCreateMutation } from 'src/mutations/tenant';

import Iconify from 'src/components/iconify';
import RHFPhone from 'src/components/hook-form/rhf-phone';
import FormProvider from 'src/components/hook-form/form-provider';
import { RHFPassword, RHFTextField } from 'src/components/hook-form';

import { ITenantNewData, ITenantNewForm } from 'src/types/tenant';

// ---------------------------------------------------------

export function TenantNewForm() {
  const { push } = useRouter();

  const mdUp = useResponsive('up', 'md');

  const defaultValues: ITenantNewData = useMemo(
    () => ({
      name: '',
      email: '',
      phone: '',

      password: '',
      responsibleName: '',
      confirmPassword: '',
    }),
    []
  );

  const methods = useForm<ITenantNewData>({
    resolver: yupResolver<ITenantNewData>(NewTenantSchema),
    defaultValues,
  });

  const { reset, handleSubmit } = methods;

  const tenantCreateMutation = useTenantCreateMutation({
    callbackFn: () => {
      reset();

      push(paths.dashboard.admin.tenant.root);
    },
  });

  const onSubmit = handleSubmit(async (data) => {
    const newTenant: ITenantNewForm = {
      name: data.name,
      email: data.email,
      phone: data.phone,

      password: data.password,
      responsibleName: data.responsibleName,
    };

    await tenantCreateMutation.mutateAsync({ newTenant });
  });

  return (
    <FormProvider methods={methods} onSubmit={onSubmit}>
      <Grid container spacing={3}>
        {mdUp && (
          <Grid md={3}>
            <Typography variant="h6" sx={{ mb: 0.5 }}>
              Criar empresa
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              Dados padrão de uma empresa.
            </Typography>
          </Grid>
        )}

        <Grid xs={12} md={9}>
          <Card>
            {!mdUp && <CardHeader title="Criar empresa" />}

            <Stack spacing={3} sx={{ p: 3 }}>
              <RHFTextField name="name" label="Nome" />
              <RHFPhone name="phone" label="Telefone celular" />
            </Stack>
          </Card>
        </Grid>

        {mdUp && (
          <Grid md={3}>
            <Typography variant="h6" sx={{ mb: 0.5 }}>
              Login de acesso para empresa
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              O mesmo endereço de e-mail de acesso será o da empresa!
            </Typography>
          </Grid>
        )}

        <Grid xs={12} md={9}>
          <Card>
            {!mdUp && <CardHeader title="Login de acesso para empresa" />}

            <Stack spacing={3} sx={{ p: 3 }}>
              <RHFTextField name="responsibleName" label="Nome do gerente" />

              <RHFTextField
                name="email"
                label="Email"
                helperText={
                  <Stack component="span" direction="row" alignItems="center">
                    <Iconify icon="eva:info-fill" width={16} sx={{ mr: 0.5 }} />O mesmo endereço de
                    e-mail de acesso será o da empresa!
                  </Stack>
                }
              />
              <Box
                rowGap={3}
                columnGap={2}
                display="grid"
                gridTemplateColumns={{
                  xs: 'repeat(1, 1fr)',
                  sm: 'repeat(2, 1fr)',
                }}
              >
                <RHFPassword name="password" label="Senha de acesso" />

                <RHFPassword name="confirmPassword" label="Confirme senha" />
              </Box>
            </Stack>
          </Card>
        </Grid>

        {mdUp && <Grid md={4} />}

        <Grid
          xs={12}
          md={8}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
          }}
        >
          <Button
            variant="contained"
            size="large"
            color="error"
            component={RouterLink}
            href={paths.dashboard.admin.tenant.root}
            sx={{ marginRight: 3 }}
          >
            Cancelar
          </Button>

          <LoadingButton
            size="large"
            type="submit"
            variant="contained"
            loading={tenantCreateMutation.isPending}
          >
            Criar Empresa
          </LoadingButton>
        </Grid>
      </Grid>
    </FormProvider>
  );
}
