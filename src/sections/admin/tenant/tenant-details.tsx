import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';

import { paths } from 'src/routes/paths';

import { fMaskPhone } from 'src/utils/format-phone';

import { useTenantShowQuery } from 'src/queries/tenant';

import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';

// --------------------------------------------------

type ComponentProps = {
  tenantId: string;
};

export default function TenantDetails({ tenantId }: ComponentProps) {
  const { themeStretch } = useSettingsContext();

  const { data: tenant } = useTenantShowQuery(tenantId);

  return (
    <Container maxWidth={themeStretch ? false : 'xl'}>
      <CustomBreadcrumbs
        heading="Detalhes da empresa"
        links={[
          { name: 'Dashboard', href: paths.dashboard.root },
          { name: 'Empresas', href: paths.dashboard.admin.tenant.root },
          { name: `${tenant.name}` },
        ]}
        sx={{ mb: 3 }}
      />

      <Card sx={{ minHeight: 200 }}>
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={4}>
              <Typography paragraph variant="overline" sx={{ color: 'text.disabled' }}>
                Nome
              </Typography>
              <Typography variant="body2">{tenant.name}</Typography>
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Typography paragraph variant="overline" sx={{ color: 'text.disabled' }}>
                Email
              </Typography>
              <Typography variant="body2">{tenant.email}</Typography>
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Typography paragraph variant="overline" sx={{ color: 'text.disabled' }}>
                Contato
              </Typography>
              <Typography variant="body2">{fMaskPhone(tenant.phone)}</Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
