import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { yupResolver } from '@hookform/resolvers/yup';

// @mui
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Unstable_Grid2';
import Typography from '@mui/material/Typography';
import CardHeader from '@mui/material/CardHeader';
import LoadingButton from '@mui/lab/LoadingButton';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { useResponsive } from 'src/hooks/use-responsive';

import { fMaskPhone } from 'src/utils/format-phone';

import { UpdateTenantSchema } from 'src/schemas/admin/tenant';
import { useTenantUpdateMutation } from 'src/mutations/tenant';

import Iconify from 'src/components/iconify';
import { RHFPhone, RHFTextField } from 'src/components/hook-form';
import FormProvider from 'src/components/hook-form/form-provider';

import { ITenant, ITenantUpdateForm } from 'src/types/tenant';

type ComponentProps = {
  currentTenant: ITenant;
};

export function TenantEditForm({ currentTenant }: ComponentProps) {
  const { push } = useRouter();

  const mdUp = useResponsive('up', 'md');

  const defaultValues: ITenantUpdateForm = useMemo(
    () => ({
      name: currentTenant?.name ?? '',
      email: currentTenant?.email ?? '',
      phone: fMaskPhone(currentTenant?.phone) ?? '',
    }),
    [currentTenant]
  );

  const methods = useForm<ITenantUpdateForm>({
    resolver: yupResolver<ITenantUpdateForm>(UpdateTenantSchema),
    defaultValues,
  });

  const { reset, handleSubmit } = methods;

  const tenantUpdateMutation = useTenantUpdateMutation({
    callbackFn: () => {
      reset();

      push(paths.dashboard.admin.tenant.root);
    },
  });

  const onSubmit = handleSubmit(async (data) => {
    const updatedTenant: ITenantUpdateForm = {
      name: data.name || undefined,
      email: data.email || undefined,
      phone: data.phone || undefined,
    };

    tenantUpdateMutation.mutateAsync({ tenantId: currentTenant.id, updatedTenant });
  });

  return (
    <FormProvider methods={methods} onSubmit={onSubmit}>
      <Grid container spacing={3}>
        {mdUp && (
          <Grid md={3}>
            <Typography variant="h6" sx={{ mb: 0.5 }}>
              Edição de empresa
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              Dados padrão de uma empresa.
            </Typography>
          </Grid>
        )}

        <Grid xs={12} md={9}>
          <Card>
            {!mdUp && <CardHeader title="Edição de empresa" />}

            <Stack spacing={3} sx={{ p: 3 }}>
              <RHFTextField name="name" label="Nome" />
              <RHFPhone name="phone" label="Telefone/celular" />
              <RHFTextField
                name="email"
                label="Email"
                helperText={
                  <Stack component="span" direction="row" alignItems="center">
                    <Iconify icon="eva:info-fill" width={16} sx={{ mr: 0.5 }} />O mesmo endereço de
                    e-mail de acesso será o da empresa!
                  </Stack>
                }
              />
            </Stack>
          </Card>
        </Grid>

        <Grid
          xs={12}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
          }}
        >
          <Button
            variant="contained"
            size="large"
            color="error"
            component={RouterLink}
            href={paths.dashboard.admin.tenant.root}
            sx={{ marginRight: 3 }}
          >
            Cancelar
          </Button>

          <LoadingButton
            type="submit"
            variant="contained"
            size="large"
            loading={tenantUpdateMutation.isPending}
          >
            Alterar Empresa
          </LoadingButton>
        </Grid>
      </Grid>
    </FormProvider>
  );
}
