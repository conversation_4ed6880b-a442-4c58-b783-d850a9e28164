'use client';

// @mui
import Container from '@mui/material/Container';

// routes
import { paths } from 'src/routes/paths';

// components
import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';

import { TenantNewForm } from '../tenant-new-form';

// ----------------------------------------------------------------------

export default function TenantNewView() {
  const settings = useSettingsContext();

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <CustomBreadcrumbs
        heading="Nova Empresa"
        links={[
          { name: 'Início', href: paths.dashboard.root },
          {
            name: 'Lista',
            href: paths.dashboard.admin.tenant.root,
          },
          { name: 'Nova Empresa' },
        ]}
        sx={{
          mb: { xs: 3, md: 5 },
        }}
      />

      <TenantNewForm />
    </Container>
  );
}
