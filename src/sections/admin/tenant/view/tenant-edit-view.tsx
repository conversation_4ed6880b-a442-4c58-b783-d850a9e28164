'use client';

// @mui
import Container from '@mui/material/Container';

// routes
import { paths } from 'src/routes/paths';

import { useTenantShowQuery } from 'src/queries/tenant';

// components
import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';

import { TenantEditForm } from '../tenant-edit-form';

type Props = {
  tenantId: string;
};

// ----------------------------------------------------------------------

export default function TenantEditView({ tenantId }: Props) {
  const settings = useSettingsContext();

  const { data: currentTenant } = useTenantShowQuery(tenantId, { retry: 1 });

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <CustomBreadcrumbs
        heading="Editar Empresa"
        links={[
          { name: 'In<PERSON>cio', href: paths.dashboard.root },
          {
            name: 'Lista',
            href: paths.dashboard.admin.tenant.root,
          },
          { name: currentTenant.name },
        ]}
        sx={{
          mb: { xs: 3, md: 5 },
        }}
      />

      <TenantEditForm currentTenant={currentTenant} />
    </Container>
  );
}
