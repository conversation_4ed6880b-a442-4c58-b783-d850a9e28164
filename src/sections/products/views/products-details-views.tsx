'use client';

import { capitalize } from 'lodash';

import Container from '@mui/material/Container';

import { paths } from 'src/routes/paths';

import { useProductShowSuspenseQuery } from 'src/queries/product';

import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';

import ProductDetails from '../product-details';

type ViewProps = {
  productId: string;
};

export default function ProductsDetailsView({ productId }: ViewProps) {
  const { themeStretch } = useSettingsContext();

  const { data: product } = useProductShowSuspenseQuery({ productId });

  return (
    <Container maxWidth={themeStretch ? false : 'xl'}>
      <CustomBreadcrumbs
        heading="Detalhes do produto"
        links={[
          {
            href: paths.dashboard.root,
            name: 'Início',
          },
          {
            name: 'Produtos',
            href: paths.dashboard.product.root,
          },
          {
            name: capitalize(product.name),
          },
        ]}
        sx={{
          mb: {
            xs: 3,
            md: 5,
          },
        }}
      />

      <ProductDetails product={product} />
    </Container>
  );
}
