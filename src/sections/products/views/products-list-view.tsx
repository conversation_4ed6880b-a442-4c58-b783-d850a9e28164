'use client';

import { keepPreviousData } from '@tanstack/react-query';

import Container from '@mui/material/Container';
import LoadingButton from '@mui/lab/LoadingButton';

import { paths } from 'src/routes/paths';

import { useProductListQuery } from 'src/queries/product';
import MagazordIcon from 'src/assets/icons/magazord-icon';
import { useMagazordSyncProductsMutation } from 'src/mutations/magazord/magazord-mutation';

import { useTable } from 'src/components/table';
import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';

import ProductsDataGrid from '../data-grid/products-data-grid';

export default function ProductsListView() {
  const table = useTable();

  const { themeStretch } = useSettingsContext();

  const magazordSyncProductsMutation = useMagazordSyncProductsMutation();

  const handleSyncProducts = async () => {
    await magazordSyncProductsMutation.mutateAsync();
  };

  const { data: { metadata, products } = { metadata: null, products: [] }, ...queryProps } =
    useProductListQuery(
      {
        params: {
          page: table.page + 1,
          limit: table.rowsPerPage,
        },
      },
      {
        placeholderData: keepPreviousData,
      }
    );

  return (
    <Container
      maxWidth={themeStretch ? false : 'xl'}
      sx={{
        flexGrow: 1,
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <CustomBreadcrumbs
        heading="Produtos"
        links={[
          {
            href: paths.dashboard.root,
            name: 'Início',
          },
          {
            name: 'Produtos',
          },
        ]}
        action={
          // TODO: LATER ADD GUARD TO SYNC ONLY WHEN HAS PERMISSION
          <LoadingButton
            variant="contained"
            loadingPosition="end"
            onClick={handleSyncProducts}
            loading={magazordSyncProductsMutation.isPending}
            disabled={magazordSyncProductsMutation.isPending}
            startIcon={<MagazordIcon fixedViewBox width={24} height={24} />}
          >
            Sincronizar produtos
          </LoadingButton>
        }
        sx={{
          mb: 3,
        }}
      />

      <ProductsDataGrid
        table={table}
        products={products}
        metadata={metadata}
        queryProps={queryProps}
      />
    </Container>
  );
}
