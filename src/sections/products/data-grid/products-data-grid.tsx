'use client';

import { useRouter } from 'next/navigation';
import { useState, useCallback } from 'react';
import { UseQueryResult } from '@tanstack/react-query';

import Card from '@mui/material/Card';
import LoadingButton from '@mui/lab/LoadingButton';
import MuiPagination from '@mui/material/Pagination';
import LinearProgress from '@mui/material/LinearProgress';
import { TablePaginationProps } from '@mui/material/TablePagination';
import {
  DataGrid,
  GridColDef,
  DataGridProps,
  GridPagination,
  useGridSelector,
  useGridApiContext,
  GridToolbarExport,
  GridActionsCellItem,
  GridToolbarContainer,
  gridPageCountSelector,
  GridToolbarFilterButton,
  GridToolbarColumnsButton,
  GridToolbarDensitySelector,
  gridPaginationModelSelector,
} from '@mui/x-data-grid';

import { paths } from 'src/routes/paths';

import { useBoolean } from 'src/hooks/use-boolean';

import { useProductDeleteMutation } from 'src/mutations/product';

import Iconify from 'src/components/iconify';
import { TableProps } from 'src/components/table';
import EmptyContent from 'src/components/empty-content';
import { ConfirmDialog } from 'src/components/custom-dialog';

import { IProduct } from 'src/types/product';
import { IMetadata, IPagination, IProductPagination } from 'src/types/pagination';

import { RenderCellProduct } from './products-table-row';

// --------------------------------------------------

type ProductFields = keyof IProduct;

type ComponentProps = {
  table: TableProps;
  metadata: IMetadata | null;
  products: IProduct[];
  queryProps: Omit<UseQueryResult<IPagination<IProductPagination>>, 'data'>;
} & Omit<DataGridProps<IProduct>, 'rows' | 'columns'>;

export default function ProductsDataGrid({
  table,
  metadata,
  products,
  queryProps,
}: ComponentProps) {
  const router = useRouter();

  const confirmDeleteProduct = useBoolean();

  const [productToDelete, setProductToDelete] = useState<IProduct | null>(null);

  const handleEditRow = useCallback(
    (productId: string) => {
      router.push(paths.dashboard.product.edit(productId));
    },
    [router]
  );

  const handleViewRow = useCallback(
    (productId: string) => {
      router.push(paths.dashboard.product.show(productId));
    },
    [router]
  );

  const deleteProductMutation = useProductDeleteMutation();

  const handleDeleteRow = useCallback(
    async (productId: string) => {
      await deleteProductMutation.mutateAsync({ productId });

      setProductToDelete(null);
    },
    [deleteProductMutation]
  );

  const columns: GridColDef<IProduct, ProductFields, ProductFields>[] = [
    {
      field: 'id',
      headerName: '#',
      hideable: true,
    },
    {
      field: 'name',
      headerName: 'Nome',
      width: 695,
      renderCell: (params) => <RenderCellProduct params={params} />,
    },
    {
      field: 'active',
      type: 'boolean',
      headerName: 'Ativo',
      width: 100,
    },
    {
      field: 'measurementUnit',
      headerName: 'Medida',
      width: 100,
    },
    {
      type: 'number',
      field: 'weight',
      headerName: 'Peso',
      width: 200,
    },

    {
      type: 'number',
      field: 'length',
      headerName: 'Profundidade',
      width: 200,
    },

    {
      type: 'number',
      field: 'width',
      headerName: 'Largura',
      width: 200,
    },
    {
      type: 'number',
      field: 'height',
      headerName: 'Altura',
      width: 200,
    },
    {
      type: 'actions',
      field: 'actions',
      headerName: 'Ações',
      align: 'right',
      headerAlign: 'right',
      width: 140,
      sortable: false,
      filterable: false,
      disableColumnMenu: true,
      getActions: (params) => [
        <GridActionsCellItem
          key="product-details"
          showInMenu
          label="Ver detalhes"
          icon={<Iconify icon="solar:eye-bold" />}
          onClick={() => handleViewRow(params.row.id)}
        />,
        // <GridActionsCellItem
        //   key="product-edit"
        //   showInMenu
        //   label="Alterar"
        //   icon={<Iconify icon="solar:pen-bold" />}
        //   onClick={() => handleEditRow(params.row.id)}
        // />,
        <GridActionsCellItem
          key="product-remove"
          showInMenu
          label="Remover"
          sx={{ color: 'error.main' }}
          onClick={() => {
            setProductToDelete(params.row);
            confirmDeleteProduct.onTrue();
          }}
          icon={<Iconify icon="solar:trash-bin-trash-bold" />}
        />,
      ],
    },
  ];

  return (
    <Card
      sx={{
        flexGrow: 1,
        display: { md: 'flex' },
        height: { xs: 600, md: 2 },
        flexDirection: { md: 'column' },
      }}
    >
      <DataGrid
        pagination
        paginationMode="server"
        rows={products}
        columns={columns}
        loading={queryProps.isPending}
        rowCount={metadata?.total}
        paginationModel={{
          page: metadata?.page ? metadata.page - 1 : 0,
          pageSize: metadata?.limit ?? table.rowsPerPage,
        }}
        onPaginationModelChange={(model) => {
          table.setPage(model.page);
          table.setRowsPerPage(model.pageSize);
        }}
        pageSizeOptions={table.rowsPerPageOptions}
        slots={{
          toolbar: GridCustomToolbar,
          pagination: CustomPagination,
          loadingOverlay: LinearProgress,
          noRowsOverlay: () => <EmptyContent title="Sem produtos" />,
          noResultsOverlay: () => <EmptyContent title="Nenhum resultado encontrado" />,
        }}
      />

      {productToDelete && (
        <ConfirmDialog
          title="Remover produto"
          open={confirmDeleteProduct.value}
          onClose={() => {
            confirmDeleteProduct.onFalse();
            setProductToDelete(null);
          }}
          content={`Tem certeza que deseja remover o produto "${productToDelete.name}"`}
          action={
            <LoadingButton
              color="error"
              variant="contained"
              loadingPosition="start"
              loading={deleteProductMutation.isPending}
              onClick={() => handleDeleteRow(productToDelete.id)}
              startIcon={<Iconify icon="material-symbols:delete" />}
            >
              Remover
            </LoadingButton>
          }
        />
      )}
    </Card>
  );
}

function Pagination({ className }: Pick<TablePaginationProps, 'className'>) {
  const apiRef = useGridApiContext();
  const pageCount = useGridSelector(apiRef, gridPageCountSelector);
  const paginationModel = useGridSelector(apiRef, gridPaginationModelSelector);

  return (
    <MuiPagination
      color="primary"
      showLastButton
      showFirstButton
      boundaryCount={2}
      count={pageCount}
      className={className}
      page={paginationModel.page + 1}
      onChange={(event, value) => apiRef.current.setPage(value - 1)}
    />
  );
}

function CustomPagination(props: any) {
  return <GridPagination ActionsComponent={Pagination} {...props} />;
}

function GridCustomToolbar() {
  return (
    <GridToolbarContainer>
      <GridToolbarDensitySelector />
      <GridToolbarColumnsButton />
      <GridToolbarFilterButton />
      <GridToolbarExport />
    </GridToolbarContainer>
  );
}
