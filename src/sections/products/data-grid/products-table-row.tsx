import Link from '@mui/material/Link';
import { GridCellParams } from '@mui/x-data-grid';
import ListItemText from '@mui/material/ListItemText';

import { paths } from 'src/routes/paths';

import { IProduct } from 'src/types/product';

type ParamsProps = {
  params: GridCellParams<IProduct>;
};

export function RenderCellProduct({ params }: ParamsProps) {
  return (
    <ListItemText
      disableTypography
      primary={
        <Link
          noWrap
          color="inherit"
          variant="subtitle2"
          sx={{ cursor: 'pointer' }}
          href={paths.dashboard.product.show(params.row.id)}
        >
          {params.row.name}
        </Link>
      }
      // secondary={
      //   <Box component="div" sx={{ typography: 'body2', color: 'text.disabled' }}>
      //     {params.row.categories}
      //   </Box>
      // }
      // sx={{ display: 'flex', flexDirection: 'column' }}
    />
  );
}
