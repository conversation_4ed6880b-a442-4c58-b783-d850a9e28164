import { upperCase } from 'lodash';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import Collapse from '@mui/material/Collapse';
import Accordion from '@mui/material/Accordion';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import Typography from '@mui/material/Typography';
import CardHeader from '@mui/material/CardHeader';
import IconButton from '@mui/material/IconButton';
import CardContent from '@mui/material/CardContent';
import TableContainer from '@mui/material/TableContainer';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';

import { useBoolean } from 'src/hooks/use-boolean';

import { fDateTime } from 'src/utils/format-time';

import Label from 'src/components/label';
import Iconify from 'src/components/iconify';
import Scrollbar from 'src/components/scrollbar';
import { useTable, TableNoData, TableSkeleton, TableHeadCustom } from 'src/components/table';

import { IProduct } from 'src/types/product';
import { IIdentifierList } from 'src/types/identifier';

import ProductActiveLabel from './product-active-label';
import RenderIdentifier from '../identifier/render-identifier';

type ComponentProps = {
  product: IProduct;
};

export default function ProductDetails({ product }: ComponentProps) {
  return (
    <Card>
      <CardHeader
        action={
          <Label variant="outlined" color={product.active ? 'success' : 'error'}>
            {upperCase(product.active ? 'Ativo' : 'Inativo')}
          </Label>
        }
      />
      <CardContent>
        <ProductInfo
          id={product.id}
          name={product.name}
          active={product.active}
          createdAt={product.createdAt}
          updatedAt={product.updatedAt}
          categories={product.categories}
          measurementUnit={product.measurementUnit}
        />

        <Stack spacing={2} pt={4}>
          <Accordion>
            <AccordionSummary
              id="identifiers-header"
              aria-controls="identifiers-content"
              expandIcon={<ExpandMoreIcon />}
            >
              <Stack spacing={1} direction="row">
                <Iconify icon="ic:baseline-qrcode" />

                <Typography variant="button">Identificadores</Typography>
              </Stack>
            </AccordionSummary>

            {/* TODO: UNCOMMNENT */}
            {/* <AccordionDetails>
              <ProductIdentifiers identifiers={product.indentiers ?? []} />
            </AccordionDetails> */}
          </Accordion>

          <Accordion>
            <AccordionSummary
              id="dimensions-header"
              aria-controls="dimensions-content"
              expandIcon={<ExpandMoreIcon />}
            >
              <Stack spacing={1} direction="row">
                <Iconify icon="radix-icons:dimensions" />

                <Typography variant="button">Dimensões</Typography>
              </Stack>
            </AccordionSummary>

            <AccordionDetails>
              <ProductDimensions
                width={product.weight}
                weight={product.weight}
                height={product.height}
                length={product.length}
                measurementUnit={product.measurementUnit}
              />
            </AccordionDetails>
          </Accordion>

          {/* TODO */}
          <Accordion>
            <AccordionSummary
              id="dimensions-header"
              aria-controls="dimensions-content"
              expandIcon={<ExpandMoreIcon />}
            >
              <Stack spacing={1} direction="row">
                <Iconify icon="radix-icons:dimensions" />

                <Typography variant="button">Informações do produto magazord</Typography>
              </Stack>
            </AccordionSummary>

            <AccordionDetails>
              {/* <ProductMagazordInfo
                ncm={product.ncm}
                cest={product.cest}
                code={product.code}
                type={product.type}
                name={product.name}
                keywords={product.keywords}
                releaseDate={product.releaseDate}
                fiscalOrigin={product.fiscalOrigin}
              /> */}
            </AccordionDetails>
          </Accordion>

          {/* TODO */}
          <Accordion>
            <AccordionSummary
              id="stock-header"
              aria-controls="stock-content"
              expandIcon={<ExpandMoreIcon />}
            >
              <Stack spacing={1} direction="row">
                <Iconify icon="vaadin:stock" />

                <Typography variant="button">Estoque</Typography>
              </Stack>
            </AccordionSummary>

            <AccordionDetails>
              <ProductStock productId={product.id} />
            </AccordionDetails>
          </Accordion>
        </Stack>
      </CardContent>
    </Card>
  );
}

export function ProductInfo({
  name,
  active,
  categories,
  measurementUnit,
  createdAt,
  updatedAt,
}: Omit<IProduct, 'width' | 'length' | 'height' | 'weight' | 'tenantId'>) {
  return (
    <Stack spacing={2}>
      <Grid container spacing={2} rowGap={2}>
        <Grid item xs={12} md={6} lg={4} xl={3}>
          <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
            Nome do produto
          </Typography>

          <Typography variant="body2">{name}</Typography>
        </Grid>

        <Grid item xs={12} md={6} lg={4} xl={3}>
          <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
            Situação
          </Typography>

          <Typography variant="body2">
            <ProductActiveLabel active={active} />
          </Typography>
        </Grid>

        <Grid item xs={12} md={6} lg={4} xl={3}>
          <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
            Medida de unidade
          </Typography>

          <Typography variant="body2">{measurementUnit ?? 'N/A'}</Typography>
        </Grid>

        <Grid item xs={12} md={6} lg={4} xl={3}>
          <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
            Categorias
          </Typography>

          {/* TODO: CATEGORIES SHOW  */}
          <Typography variant="body2">{categories ?? 'N/A'}</Typography>
        </Grid>

        <Grid item xs={12} md={6} lg={4} xl={3}>
          <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
            Criado em:
          </Typography>

          <Typography variant="body2">{fDateTime(createdAt)}</Typography>
        </Grid>

        <Grid item xs={12} md={6} lg={4} xl={3}>
          <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
            Última alteração em:
          </Typography>

          <Typography variant="body2">{fDateTime(updatedAt)}</Typography>
        </Grid>
      </Grid>
    </Stack>
  );
}

export function ProductIdentifiers({ identifiers }: { identifiers: IIdentifierList[] }) {
  const table = useTable();

  const TABLE_HEAD = [
    { id: 'label', label: 'Nome' },
    { id: 'valueType', label: 'Tipo de identificador' },
    { id: 'createdAt', label: 'Criado em' },
    { id: 'updatedAt', label: 'Última alteração em' },
    { id: '', label: 'Pré-visualização', align: 'center' },
  ];

  return (
    <Card>
      <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
        <Scrollbar>
          <Table stickyHeader size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
            <TableHeadCustom order={table.order} orderBy={table.orderBy} headLabel={TABLE_HEAD} />

            <TableBody>
              {identifiers.map((row) => (
                <ProductIdentifierTableRow row={row} />
              ))}

              <TableNoData notFound={!identifiers.length} />
            </TableBody>
          </Table>
        </Scrollbar>
      </TableContainer>
    </Card>
  );
}

function ProductIdentifierTableRow({ row }: { row: IIdentifierList }) {
  return (
    <TableRow hover>
      <TableCell>{row.label}</TableCell>
      <TableCell>{row.valueType}</TableCell>

      <TableCell>{fDateTime(row.createdAt)}</TableCell>
      <TableCell>{fDateTime(row.updatedAt)}</TableCell>

      <TableCell align="center">
        <Tooltip
          placement="top"
          title={<RenderIdentifier value={row.value} valueType={row.valueType} />}
        >
          <Iconify icon="material-symbols:preview" sx={{ cursor: 'pointer' }} />
        </Tooltip>
      </TableCell>
    </TableRow>
  );
}

// TODO: LATER
// export function ProductMagazordInfo({ name }: IProductMagazord) {
//   return (
//     <Stack spacing={2}>
//       <Grid container spacing={2} rowGap={2}>
//         <Grid item xs={12} md={6} lg={4} xl={3}>
//           <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
//             Nome do produto
//           </Typography>

//           <Typography variant="body2">{name}</Typography>
//         </Grid>

//         <Grid item xs={12} md={6} lg={4} xl={3}>
//           <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
//             Data de lançamento
//           </Typography>

//           <Typography variant="body2">
//             {isDate(releaseDate) ? fDateTime(releaseDate) : 'Não informada'}
//           </Typography>
//         </Grid>

//         <Grid item xs={12} md={6} lg={4} xl={3}>
//           <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
//             Origem fiscal
//           </Typography>

//           <Typography variant="body2">{fiscalOrigin ?? 'N/A'}</Typography>
//         </Grid>

//         <Grid item xs={12} md={6} lg={4} xl={3}>
//           <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
//             Tipo do produto
//           </Typography>

//           <Typography variant="body2">{type ?? 'N/A'}</Typography>
//         </Grid>

//         <Grid item xs={12} md={6} lg={4} xl={3}>
//           <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
//             Código do produto
//           </Typography>

//           <Typography variant="body2">{code ?? 'N/A'}</Typography>
//         </Grid>

//         <Grid item xs={12} md={6} lg={4} xl={3}>
//           <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
//             NCM
//           </Typography>

//           <Typography variant="body2">{ncm ?? 'N/A'}</Typography>
//         </Grid>

//         <Grid item xs={12} md={6} lg={4} xl={3}>
//           <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
//             CEST
//           </Typography>

//           <Typography variant="body2">{cest ?? 'N/A'}</Typography>
//         </Grid>

//         <Grid item xs={12} md={6} lg={4} xl={3}>
//           <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
//             Palavras-chave
//           </Typography>

//           <Typography variant="body2">{keywords ?? 'N/A'}</Typography>
//         </Grid>
//       </Grid>
//     </Stack>
//   );
// }

export function ProductDimensions({
  height,
  width,
  length,
  weight,
  measurementUnit,
}: Pick<IProduct, 'height' | 'length' | 'weight' | 'width' | 'measurementUnit'>) {
  return (
    <Stack spacing={2}>
      <Grid container spacing={2} rowGap={2}>
        <Grid item xs={12} md={6} lg={4} xl={3}>
          <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
            Altura
          </Typography>

          <Typography variant="body2">{height ?? 'N/A'}</Typography>
        </Grid>

        <Grid item xs={12} md={6} lg={4} xl={3}>
          <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
            Largura
          </Typography>

          <Typography variant="body2">{width ?? 'N/A'}</Typography>
        </Grid>

        <Grid item xs={12} md={6} lg={4} xl={3}>
          <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
            Comprimento
          </Typography>

          <Typography variant="body2">{length ?? 'N/A'}</Typography>
        </Grid>

        <Grid item xs={12} md={6} lg={4} xl={3}>
          <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
            Peso
          </Typography>

          <Typography variant="body2">{weight ?? 'N/A'}</Typography>
        </Grid>

        <Grid item xs={12} md={6} lg={4} xl={3}>
          <Typography paragraph variant="overline" sx={{ color: 'text.secondary' }}>
            Unidade de medida
          </Typography>

          <Typography variant="body2">{measurementUnit ?? 'N/A'}</Typography>
        </Grid>
      </Grid>
    </Stack>
  );
}

export function ProductStock({ productId }: { productId: string }) {
  const table = useTable();

  // const [tableData, setTableData] = useState([]);

  const {
    data: productStock = [{ warehouse: 'Armazém 1', position: 'W1-R1-M1-P1', quantity: 3 }],
    isPending = false,
  } = {
    data: [{ warehouse: 'Armazém 1', position: 'W1-R1-M1-P1', quantity: 3 }],
    isPending: false,
  };
  // const { data: productStock = [] } = useProductStock({ tenantId,  productId });

  // useEffect(() => {
  //   if (!isEqual(tableData, productStock)) {
  //     setTableData(productStock);
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [productStock]);

  const TABLE_HEAD = [
    { id: 'warehouse', label: 'Armazém' },
    { id: 'quantity', label: 'Quantidade' },
  ];

  return (
    <Card>
      <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
        <Scrollbar>
          <Table stickyHeader size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
            <TableHeadCustom
              withCollapseButton
              order={table.order}
              orderBy={table.orderBy}
              headLabel={TABLE_HEAD}
            />

            <TableBody>
              {isPending
                ? Array.from({ length: table.rowsPerPage }).map((_, index) => (
                    <TableSkeleton key={`table-row-skeleton-${index}`} />
                  ))
                : productStock.map((row) => <ProductWarehouseStockTableRow row={row} />)}

              <TableNoData notFound={!productStock.length} />
            </TableBody>
          </Table>
        </Scrollbar>
      </TableContainer>
    </Card>
  );
}

function ProductWarehouseStockTableRow({ row }: { row: any }) {
  const table = useTable();
  const open = useBoolean();

  const TABLE_HEAD = [
    { id: 'warehouse', label: 'Armazém' },
    { id: 'position', label: 'Posição' },
    { id: 'quantity', label: 'Quantidade' },
  ];

  return (
    <>
      <TableRow hover>
        <TableCell>
          <IconButton aria-label="expand row" size="small" onClick={open.onToggle}>
            {open.value ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
        </TableCell>
        <TableCell>{row.warehouse}</TableCell>
        <TableCell>{row.quantity}</TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={6}>
          <Collapse in={open.value} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1 }}>
              <Typography variant="h6" gutterBottom component="div">
                Quantidade por posição
              </Typography>
              <Table stickyHeader size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
                <TableHeadCustom
                  order={table.order}
                  orderBy={table.orderBy}
                  headLabel={TABLE_HEAD}
                />
                <TableBody>
                  <TableRow>
                    <TableCell component="th" scope="row">
                      Armazém 1
                    </TableCell>
                    <TableCell>A1</TableCell>
                    <TableCell>1</TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell component="th" scope="row">
                      Armazém 1
                    </TableCell>
                    <TableCell>A2</TableCell>
                    <TableCell>2</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  );
}
