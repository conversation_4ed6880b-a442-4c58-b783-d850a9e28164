import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';

import { fDateTime } from 'src/utils/format-time';

import Image from 'src/components/image';
import Iconify from 'src/components/iconify';

import { ITag } from 'src/types/tag';

import TagTypeLabel from '../tag-type-label';

type Props = {
  row: ITag;
};

export default function TagTableRow({ row }: Props) {
  const { name, type, value, createdAt, updatedAt } = row;

  const imagePreview = `https://api.labelary.com/v1/printers/8dpmm/labels/4x6/0/${value}`;

  return (
    <TableRow hover>
      <TableCell sx={{ whiteSpace: 'nowrap' }}>{name}</TableCell>
      <TableCell sx={{ whiteSpace: 'nowrap' }}>
        <TagTypeLabel type={type} />
      </TableCell>

      <TableCell sx={{ whiteSpace: 'nowrap' }}>{fDateTime(createdAt)}</TableCell>
      <TableCell sx={{ whiteSpace: 'nowrap' }}>{fDateTime(updatedAt)}</TableCell>
      <TableCell align="center" sx={{ whiteSpace: 'nowrap' }}>
        <Tooltip placement="left" title={<Image src={imagePreview} />}>
          <Iconify icon="mdi:eye" sx={{ cursor: 'pointer' }} />
        </Tooltip>
      </TableCell>
    </TableRow>
  );
}
