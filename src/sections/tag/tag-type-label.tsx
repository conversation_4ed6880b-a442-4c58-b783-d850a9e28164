import { TagType } from 'src/enum/tag';

import Label from 'src/components/label';

type TagTypeLabelProps = {
  type: TagType;
};

export default function TagTypeLabel({ type }: TagTypeLabelProps) {
  switch (type) {
    case TagType.NFE:
      return <Label color="info">NFE reduzida</Label>;

    case TagType.MOVER:
      return <Label color="primary">Movimentador</Label>;

    case TagType.VOLUME:
      return <Label color="secondary">Volume</Label>;

    case TagType.PRODUCT:
      return <Label color="success">Produto</Label>;

    case TagType.POSITION:
      return <Label color="warning">Posição</Label>;

    case TagType.PACKAGE:
      return <Label color="error">Embalagem</Label>;

    default:
      return <Label color="default">N/A</Label>;
  }
}
