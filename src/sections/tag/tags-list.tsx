import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import Container from '@mui/material/Container';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';

import { paths } from 'src/routes/paths';

import { useTagListSuspenseQuery } from 'src/queries/tag.query';

import Scrollbar from 'src/components/scrollbar';
import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';
import {
  useTable,
  TableNoData,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

import TagTableRow from './table/tag-table-row';

const TABLE_HEAD = [
  { id: 'name', label: 'Nome', width: 200 },
  { id: 'type', label: 'Tipo', width: 220 },
  { id: 'createdAt', label: 'Criado em', width: 100 },
  { id: 'updatedAt', label: 'Última alteração em', width: 100 },
  { id: '', label: 'Ações', width: 88, align: 'center' },
];

export default function TagsList() {
  const table = useTable();

  const { themeStretch } = useSettingsContext();

  const { data: tags = [] } = useTagListSuspenseQuery();

  const notFound = !tags.length;
  const totalPages = Math.floor(tags.length / table.rowsPerPage);

  return (
    <Container maxWidth={themeStretch ? false : 'lg'}>
      <CustomBreadcrumbs
        heading="Modelos de Etiquetas"
        links={[{ name: 'Início', href: paths.dashboard.root }, { name: 'Modelos' }]}
        sx={{
          mb: { sm: 5, lg: 3 },
        }}
      />

      <Card>
        <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
          <Scrollbar>
            <Table stickyHeader size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
              <TableHeadCustom order={table.order} orderBy={table.orderBy} headLabel={TABLE_HEAD} />

              <TableBody>
                {tags
                  .slice(
                    table.page * table.rowsPerPage,
                    table.page * table.rowsPerPage + table.rowsPerPage
                  )
                  .map((row) => (
                    <TagTableRow key={row.id} row={row} />
                  ))}

                <TableNoData notFound={notFound} />
              </TableBody>
            </Table>
          </Scrollbar>
        </TableContainer>

        <TablePaginationCustom
          page={table.page}
          count={tags.length}
          totalPages={totalPages}
          rowsPerPage={table.rowsPerPage}
          onPageChange={table.onChangePage}
          onRowsPerPageChange={table.onChangeRowsPerPage}
          //
          dense={table.dense}
          onChangeDense={table.onChangeDense}
        />
      </Card>
    </Container>
  );
}
