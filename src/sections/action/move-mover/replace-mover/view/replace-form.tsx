'use client';

import { useForm } from 'react-hook-form';
import { useRef, useState, useEffect } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';

import { Box } from '@mui/system';
import { LoadingButton } from '@mui/lab';
import Container from '@mui/material/Container';
import { Card, Grid, Alert } from '@mui/material';

import { paths } from 'src/routes/paths';

import { IdentifierType } from 'src/enum/identifier';
import { useCompanyContext } from 'src/company/hooks';
import { IReplaceMoverFormSchema } from 'src/schemas/move-movers';
import { useReplaceMoverMutation } from 'src/mutations/mover/mover-mutation';
import { useIdentifierShowQuery } from 'src/queries/identifier/identifier-query';

import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';
import FormProvider, { RHFTextField } from 'src/components/hook-form';

import { IReplaceMoverForm } from 'src/types/mover-mover';

export default function ReplaceMoverView() {
  const toStockPositionRef = useRef<HTMLInputElement>(null);
  const moverRef = useRef<HTMLInputElement>(null);

  const settings = useSettingsContext();
  const { currentCompany } = useCompanyContext();

  const [toStockPositionName, setToStockPositionName] = useState<string>('');
  const [moverIdName, setMoverIdName] = useState<string>('');

  const defaultValues: IReplaceMoverForm = {
    moverId: '',
    toStockPositionId: null,
  };

  const methods = useForm<IReplaceMoverForm>({
    resolver: yupResolver<IReplaceMoverForm>(IReplaceMoverFormSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
    watch,
    setValue,
  } = methods;

  const moverId = watch('moverId');
  const toStockPositionId = watch('toStockPositionId');

  // Queries
  const { data: moverData } = useIdentifierShowQuery({ value: moverId }, { enabled: !!moverId });

  const { data: toStockPositionData } = useIdentifierShowQuery(
    { value: toStockPositionId || '' },
    { enabled: !!toStockPositionId }
  );

  useEffect(() => {
    if (moverData?.type === IdentifierType.MOVER) {
      setValue('moverId', moverData?.item.id);
      setMoverIdName(moverData?.item.name);
    }
  }, [moverData, setValue]);

  useEffect(() => {
    if (toStockPositionData?.type === IdentifierType.POSITION) {
      setValue('toStockPositionId', toStockPositionData.item.id);
      setToStockPositionName(toStockPositionData.item.name);
    }
  }, [toStockPositionData, setValue]);

  useEffect(() => {
    if (moverId && toStockPositionRef.current) {
      toStockPositionRef.current.focus();
    }
  }, [moverId]);

  const { mutateAsync } = useReplaceMoverMutation({
    callbackFn: () => {
      reset();
    },
  });

  const onSubmit = handleSubmit(async (data) => {
    await mutateAsync({
      companyId: currentCompany?.id!,
      warehouseId: moverData?.item.warehouseId!,
      moverId: data.moverId,
      toStockPositionId: data.toStockPositionId ?? null,
    });
  });

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <CustomBreadcrumbs
        heading="Movimentar movimentador"
        links={[
          { name: 'Dashboard', href: paths.dashboard.root },
          { name: 'Ações', href: paths.dashboard.actions.root },
          { name: 'Movimentar movimentador', href: paths.dashboard.actions.move.mover.root },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card sx={{ p: 3, display: 'flex', flexDirection: 'column', gap: 3 }}>
        <Alert severity="info">
          Deixe o campo de posição de destino em branco para desvincular o movimentador da posição
          atual.
        </Alert>

        <FormProvider methods={methods} onSubmit={onSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <RHFTextField
                name="moverId"
                label="Movimentador"
                helperText={moverIdName}
                placeholder="Selecione o movimentador"
                inputRef={moverRef}
                autoFocus
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <RHFTextField
                name="toStockPositionId"
                label="Posição de destino"
                helperText={toStockPositionName}
                placeholder="Selecione a posição de destino"
                inputRef={toStockPositionRef}
              />
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                <LoadingButton type="submit" variant="contained" loading={isSubmitting}>
                  Movimentar movimentador
                </LoadingButton>
              </Box>
            </Grid>
          </Grid>
        </FormProvider>
      </Card>
    </Container>
  );
}
