'use client';

import { Grid } from '@mui/material';
import Container from '@mui/material/Container';

import { paths } from 'src/routes/paths';

import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';

import { ActionWidgetSummary } from 'src/sections/overview/course/course-widget-summary';

export default function ActionView() {
  const settings = useSettingsContext();

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <CustomBreadcrumbs
        heading="Ações"
        links={[
          { name: 'Dashboard', href: paths.dashboard.root },
          { name: 'A<PERSON>ões', href: paths.dashboard.actions.root },
        ]}
        sx={{
          mb: { xs: 3, md: 5 },
        }}
      />
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6}>
          <ActionWidgetSummary
            icon="/assets/icons/navbar/ic_product.svg"
            title="Movimentar produto"
            onClick={() => {
              window.location.href = paths.dashboard.actions.move.product.root;
            }}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <ActionWidgetSummary
            icon="/assets/icons/navbar/ic_movers.svg"
            title="Movimentar movimentador"
            onClick={() => {
              window.location.href = paths.dashboard.actions.move.mover.replace;
            }}
          />
        </Grid>
      </Grid>
    </Container>
  );
}
