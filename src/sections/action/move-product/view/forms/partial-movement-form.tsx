'use client';

import { useForm } from 'react-hook-form';
import { useRef, useState, useEffect } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';

import { LoadingButton } from '@mui/lab';
import { Box, Grid, Stack, Button } from '@mui/material';

import { IdentifierType } from 'src/enum/identifier';
import { usePartialMoveProductMutation } from 'src/mutations/movements';
import { PartialMoveProductFormSchema } from 'src/schemas/move-products';
import { useIdentifierShowQuery } from 'src/queries/identifier/identifier-query';

import FormProvider, { RHFTextField } from 'src/components/hook-form';

import { IPartialMoveProductForm } from 'src/types/mover-product';

export default function PartialMovementForm() {
  const fromPositionRef = useRef<HTMLInputElement>(null);
  const toPositionRef = useRef<HTMLInputElement>(null);
  const productIdRef = useRef<HTMLInputElement>(null);
  const quantityRef = useRef<HTMLInputElement>(null);

  const [fromPositionName, setFromPositionName] = useState<string>('');
  const [toPositionName, setToPositionName] = useState<string>('');
  const [productName, setProductName] = useState<string>('');

  const defaultValues = {
    fromPosition: '',
    toPosition: '',
    productId: '',
    quantity: 1,
  };

  const methods = useForm<IPartialMoveProductForm>({
    resolver: yupResolver(PartialMoveProductFormSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
    watch,
    setValue,
  } = methods;

  const fromPositionIdentifierId = watch('fromPosition');
  const toPositionIdentifierId = watch('toPosition');
  const productId = watch('productId');

  // Queries
  const { data: fromPositionData } = useIdentifierShowQuery(
    { value: fromPositionIdentifierId },
    { enabled: !!fromPositionIdentifierId }
  );

  const { data: toPositionData } = useIdentifierShowQuery(
    { value: toPositionIdentifierId },
    { enabled: !!toPositionIdentifierId }
  );

  const { data: productData } = useIdentifierShowQuery(
    { value: productId },
    { enabled: !!productId }
  );

  useEffect(() => {
    if (productData?.type === IdentifierType.PRODUCT) {
      setProductName(productData?.item.name);
    }
  }, [productData]);

  useEffect(() => {
    if (
      fromPositionData?.type === IdentifierType.POSITION ||
      fromPositionData?.type === IdentifierType.MOVER
    ) {
      setFromPositionName(fromPositionData.item.name);
    }
  }, [fromPositionData]);

  useEffect(() => {
    if (
      toPositionData?.type === IdentifierType.POSITION ||
      toPositionData?.type === IdentifierType.MOVER
    ) {
      setToPositionName(toPositionData.item.name);
    }
  }, [toPositionData]);

  useEffect(() => {
    if (fromPositionIdentifierId && productIdRef.current) {
      productIdRef.current.focus();
    }
  }, [fromPositionIdentifierId]);

  useEffect(() => {
    if (productId && toPositionRef.current) {
      toPositionRef.current.focus();
    }
  }, [productId]);

  useEffect(() => {
    if (toPositionIdentifierId && quantityRef.current) {
      quantityRef.current.focus();
    }
  }, [toPositionIdentifierId]);

  const { mutateAsync } = usePartialMoveProductMutation({
    callbackFn: () => {
      reset();
    },
  });

  const onSubmit = handleSubmit(async (data) => {
    await mutateAsync({
      productIdentifierId: data.productId,
      fromIdentifierId: data.fromPosition,
      toIdentifierId: data.toPosition,
      quantity: data.quantity,
    });
  });

  return (
    <FormProvider methods={methods} onSubmit={onSubmit}>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <RHFTextField
            name="fromPosition"
            label="Posição de origem"
            helperText={fromPositionName}
            placeholder="Selecione a posição de origem"
            inputRef={fromPositionRef}
            autoFocus
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <RHFTextField
            name="productId"
            label="Produto"
            helperText={productName}
            placeholder="Selecione o produto"
            inputRef={productIdRef}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <RHFTextField
            name="toPosition"
            label="Posição de destino"
            helperText={toPositionName}
            placeholder="Selecione a posição de destino"
            inputRef={toPositionRef}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Stack direction="row" alignItems="center" justifyContent="center">
            <Button
              variant="contained"
              size="large"
              onClick={() => {
                const current = Number(watch('quantity')) || 1;
                if (current > 1) setValue('quantity', current - 1);
              }}
              sx={{ mr: 1 }}
            >
              -
            </Button>

            <RHFTextField
              name="quantity"
              label="Quantidade"
              placeholder="Selecione a quantidade"
              inputRef={quantityRef}
              type="number"
            />

            <Button
              variant="contained"
              size="large"
              onClick={() => {
                const current = Number(watch('quantity')) || 1;
                setValue('quantity', current + 1);
              }}
              sx={{ ml: 1 }}
            >
              +
            </Button>
          </Stack>
        </Grid>

        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <LoadingButton type="submit" variant="contained" loading={isSubmitting}>
              Movimentar produto
            </LoadingButton>
          </Box>
        </Grid>
      </Grid>
    </FormProvider>
  );
}
