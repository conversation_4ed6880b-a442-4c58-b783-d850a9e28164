'use client';

import { useForm } from 'react-hook-form';
import { useRef, useState, useEffect } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';

import { LoadingButton } from '@mui/lab';
import { Box, Grid } from '@mui/material';

import { IdentifierType } from 'src/enum/identifier';
import { useAllMoveProductMutation } from 'src/mutations/movements';
import { AllMoveProductFormSchema } from 'src/schemas/move-products';
import { useIdentifierShowQuery } from 'src/queries/identifier/identifier-query';

import FormProvider, { RHFTextField } from 'src/components/hook-form';

import { IAllMoveProductForm } from 'src/types/mover-product';

export default function AllMovementForm() {
  const fromPositionRef = useRef<HTMLInputElement>(null);
  const toPositionRef = useRef<HTMLInputElement>(null);

  const [fromPositionName, setFromPositionName] = useState<string>('');
  const [toPositionName, setToPositionName] = useState<string>('');

  const defaultValues = {
    fromPosition: '',
    toPosition: '',
  };

  const methods = useForm<IAllMoveProductForm>({
    resolver: yupResolver(AllMoveProductFormSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
    watch,
  } = methods;

  const fromPositionIdentifierId = watch('fromPosition');
  const toPositionIdentifierId = watch('toPosition');

  // Queries
  const { data: fromPositionData } = useIdentifierShowQuery(
    { value: fromPositionIdentifierId },
    { enabled: !!fromPositionIdentifierId }
  );

  const { data: toPositionData } = useIdentifierShowQuery(
    { value: toPositionIdentifierId },
    { enabled: !!toPositionIdentifierId }
  );

  useEffect(() => {
    if (
      fromPositionData?.type === IdentifierType.POSITION ||
      fromPositionData?.type === IdentifierType.MOVER
    ) {
      setFromPositionName(fromPositionData.item.name);
    }
  }, [fromPositionData]);

  useEffect(() => {
    if (
      toPositionData?.type === IdentifierType.POSITION ||
      toPositionData?.type === IdentifierType.MOVER
    ) {
      setToPositionName(toPositionData.item.name);
    }
  }, [toPositionData]);

  useEffect(() => {
    if (fromPositionIdentifierId && toPositionRef.current) {
      toPositionRef.current.focus();
    }
  }, [fromPositionIdentifierId]);

  const { mutateAsync } = useAllMoveProductMutation({
    callbackFn: () => {
      reset();
    },
  });

  const onSubmit = handleSubmit(async (data) => {
    await mutateAsync({
      fromIdentifierId: data.fromPosition,
      toIdentifierId: data.toPosition,
    });
  });

  return (
    <FormProvider methods={methods} onSubmit={onSubmit}>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <RHFTextField
            name="fromPosition"
            label="Posição de origem"
            helperText={fromPositionName}
            placeholder="Selecione a posição de origem"
            inputRef={fromPositionRef}
            autoFocus
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <RHFTextField
            name="toPosition"
            label="Posição de destino"
            helperText={toPositionName}
            placeholder="Selecione a posição de destino"
            inputRef={toPositionRef}
          />
        </Grid>

        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <LoadingButton type="submit" variant="contained" loading={isSubmitting}>
              Movimentar produto
            </LoadingButton>
          </Box>
        </Grid>
      </Grid>
    </FormProvider>
  );
}
