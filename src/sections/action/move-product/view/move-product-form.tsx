'use client';

import { useState } from 'react';

import Container from '@mui/material/Container';
import { Card, ToggleButton, ToggleButtonGroup } from '@mui/material';

import { paths } from 'src/routes/paths';

import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';

import AllMovementForm from './forms/all-movement-form';
import TotalMovementForm from './forms/total-movement-form';
import PartialMovementForm from './forms/partial-movement-form';

export default function MoveProductForm() {
  const settings = useSettingsContext();

  const [movementType, setMovementType] = useState<string | null>('partial');

  const handleMovementType = (
    event: React.MouseEvent<HTMLElement>,
    newMovementType: string | null
  ) => {
    setMovementType(newMovementType);
  };

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <CustomBreadcrumbs
        heading="Movimentar produto"
        links={[
          { name: 'Dashboard', href: paths.dashboard.root },
          { name: '<PERSON><PERSON><PERSON><PERSON>', href: paths.dashboard.actions.root },
          { name: 'Movimentar produto', href: paths.dashboard.actions.move.product.root },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card sx={{ p: 3, display: 'flex', flexDirection: 'column', gap: 3 }}>
        <ToggleButtonGroup
          value={movementType}
          exclusive
          onChange={handleMovementType}
          aria-label="product movement"
          sx={{ display: 'flex', justifyContent: 'space-evenly' }}
        >
          <ToggleButton
            value="partial"
            aria-label="partial movement"
            sx={{ fontSize: { xs: '0.65rem', sm: '0.85rem' } }}
          >
            Movimentação parcial de um produto
          </ToggleButton>
          <ToggleButton
            value="total"
            aria-label="total movement"
            sx={{ fontSize: { xs: '0.65rem', sm: '0.85rem' } }}
          >
            Movimentação total de um produto
          </ToggleButton>
          <ToggleButton
            value="all"
            aria-label="all movement"
            sx={{ fontSize: { xs: '0.65rem', sm: '0.85rem' } }}
          >
            Movimentação total de todos os produtos
          </ToggleButton>
        </ToggleButtonGroup>

        {movementType === 'partial' && <PartialMovementForm />}
        {movementType === 'total' && <TotalMovementForm />}
        {movementType === 'all' && <AllMovementForm />}
      </Card>
    </Container>
  );
}
