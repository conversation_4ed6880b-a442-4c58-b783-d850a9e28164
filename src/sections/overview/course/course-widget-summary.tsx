import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Typography from '@mui/material/Typography';
import type { CardProps } from '@mui/material/Card';

import { ColorSchema } from 'src/theme/palette';

import SvgColor from 'src/components/svg-color';

// ----------------------------------------------------------------------

type Props = CardProps & {
  icon: string;
  title: string;
  color?: ColorSchema;
};

export function ActionWidgetSummary({ sx, icon, title, color = 'warning', ...other }: Props) {
  return (
    <Card
      sx={[
        {
          py: 3,
          pl: 3,
          pr: 2.5,
          position: 'relative',
          cursor: 'pointer',
          transition: 'transform 0.3s ease',
          ':hover': { transform: 'scale(1.05)' },
        },
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
      {...other}
    >
      <Box sx={{ flexGrow: 1 }}>
        <Typography noWrap variant="h5" component="div" sx={{ color: 'text.secondary' }}>
          {title}
        </Typography>
      </Box>

      <Box
        sx={{
          top: 24,
          right: 20,
          width: 36,
          height: 36,
          position: 'absolute',
        }}
      >
        <SvgColor
          src={icon}
          sx={(theme) => ({
            background: `linear-gradient(135deg, ${theme.palette[color].main} 0%, ${theme.palette[color].dark} 100%)`,
          })}
        />
      </Box>

      <Box
        sx={(theme) => ({
          top: -44,
          width: 160,
          zIndex: -1,
          height: 160,
          right: -104,
          opacity: 0.12,
          borderRadius: 3,
          position: 'absolute',
          transform: 'rotate(40deg)',
          background: `linear-gradient(to right, ${theme.palette[color].main}, transparent)`,
        })}
      />
    </Card>
  );
}
