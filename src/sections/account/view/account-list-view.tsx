'use client';

import { useState, useEffect, useCallback } from 'react';

import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';

import { paths } from 'src/routes/paths';

import { useBoolean } from 'src/hooks/use-boolean';

import { useAccountDeleteMutation } from 'src/mutations/account';
import { useAccountSuspenseListQuery } from 'src/queries/account/account-query';

import Iconify from 'src/components/iconify';
import Scrollbar from 'src/components/scrollbar';
import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';
import {
  useTable,
  TableNoData,
  TableSkeleton,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

import { IAccount } from 'src/types/account';

import AccountTableRow from '../account-table-row';
import AccountQuickNewForm from '../account-quick-new-form';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: 'name', label: 'Name', width: 200 },
  { id: 'email', label: 'Email', width: 220 },
  { id: 'createdAt', label: 'Criado em', width: 100 },
  { id: 'updatedAt', label: 'Alterado em', width: 100 },
  { id: '', width: 88 },
];

// ----------------------------------------------------------------------

const defaultFilters = {
  name: '',
};

export default function AccountListView() {
  const settings = useSettingsContext();

  const table = useTable();

  const newAccount = useBoolean();

  const { mutateAsync } = useAccountDeleteMutation();

  const [tableData, setTableData] = useState<IAccount[]>([]);

  const {
    data: { metadata, accounts },
    isFetching,
    isRefetching,
    refetch,
  } = useAccountSuspenseListQuery({ page: table.page + 1, limit: table.rowsPerPage });

  useEffect(() => {
    setTableData(accounts || []);
  }, [accounts]);

  useEffect(() => {
    refetch();
  }, [refetch, table.page, table.rowsPerPage]);

  const notFound = !isFetching && !isRefetching && !tableData.length;

  const handleDeleteRow = useCallback(
    (accountId: string) => {
      mutateAsync({ accountId });
    },
    [mutateAsync]
  );

  return (
    <>
      <Container maxWidth={settings.themeStretch ? false : 'lg'}>
        <CustomBreadcrumbs
          heading="Usuários"
          links={[
            { name: 'Dashboard', href: paths.dashboard.root },
            { name: 'Usuários', href: paths.dashboard.account.root },
            { name: 'Lista' },
          ]}
          action={
            <Button
              color="success"
              variant="contained"
              onClick={newAccount.onTrue}
              startIcon={<Iconify icon="mingcute:add-line" />}
            >
              Novo usuário
            </Button>
          }
          sx={{
            mb: { xs: 3, md: 5 },
          }}
        />

        <Card>
          <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
            <Scrollbar>
              <Table stickyHeader size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
                <TableHeadCustom
                  order={table.order}
                  orderBy={table.orderBy}
                  headLabel={TABLE_HEAD}
                />

                <TableBody>
                  {isFetching || isRefetching
                    ? Array.from({ length: table.rowsPerPage }).map((_, index) => (
                        <TableSkeleton key={`table-row-skeleton-${index}`} />
                      ))
                    : tableData.map((row) => (
                        <AccountTableRow
                          key={row.id}
                          row={row}
                          onDeleteRow={() => handleDeleteRow(row.id)}
                        />
                      ))}

                  <TableNoData notFound={notFound} />
                </TableBody>
              </Table>
            </Scrollbar>
          </TableContainer>

          <TablePaginationCustom
            totalPages={Math.floor(tableData.length / table.rowsPerPage)}
            count={metadata.total}
            page={table.page}
            rowsPerPage={table.rowsPerPage}
            onPageChange={table.onChangePage}
            onRowsPerPageChange={table.onChangeRowsPerPage}
            //
            dense={table.dense}
            onChangeDense={table.onChangeDense}
          />
        </Card>
      </Container>

      <AccountQuickNewForm open={newAccount.value} onClose={newAccount.onFalse} />
    </>
  );
}
