import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import { InputLabel } from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { useAuthContext } from 'src/auth/hooks';
import { EnvironmentTypes } from 'src/enum/environment';
import { UpdateAccountSchema } from 'src/schemas/account';
import { useAccountUpdateMutation } from 'src/mutations/account';
import { ENVIRONMENT_TYPES_LABELED } from 'src/consts/environment';
import { useCompanyListQuery } from 'src/queries/company/company-query';
import { useTenantListWarehouseQuery } from 'src/queries/tenant/tenant-query';

import Iconify from 'src/components/iconify';
import FormProvider, { RHFSelect, RHFSwitch, RHFTextField } from 'src/components/hook-form';

import { IAccount, IAccountEditForm } from 'src/types/account';

// ----------------------------------------------------------------------

type Props = {
  open: boolean;
  onClose: VoidFunction;
  currentAccount: IAccount;
};

export default function AccountQuickEditForm({ currentAccount, open, onClose }: Props) {
  const { account } = useAuthContext();
  const tenantId = account?.tenantId!;

  const { data: companies } = useCompanyListQuery();

  const { data: warehouses } = useTenantListWarehouseQuery(tenantId);

  const defaultValues: IAccountEditForm = useMemo(
    () => ({
      name: currentAccount?.name || '',
      email: currentAccount?.email || '',
      role: currentAccount?.role || {},
      environment: currentAccount?.environment || '',
      environmentId: currentAccount?.environmentId || tenantId,
    }),
    [currentAccount, tenantId]
  );

  const methods = useForm({
    resolver: yupResolver(UpdateAccountSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
    watch,
  } = methods;

  const { mutateAsync, isPending } = useAccountUpdateMutation({
    callbackFn: () => {
      reset();
      onClose();
    },
  });

  const onSubmit = handleSubmit(async (updatedAccount) => {
    mutateAsync({ accountId: currentAccount.id, updatedAccount });
  });

  const selectedEnvironment = watch('environment');

  return (
    <Dialog
      fullWidth
      maxWidth={false}
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { maxWidth: 720 },
      }}
    >
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <DialogTitle>Alterar usuário</DialogTitle>

        <DialogContent>
          <Box
            rowGap={2}
            columnGap={2}
            paddingTop={1}
            display="grid"
            gridTemplateColumns={{
              xs: 'repeat(1, 1fr)',
              sm: 'repeat(2, 1fr)',
            }}
          >
            <RHFTextField name="name" label="Nome completo" placeholder="Fulano da Silva" />
            <RHFTextField name="email" label="Endereço de email" placeholder="<EMAIL>" />

            <RHFSelect name="environment" label="Environment">
              {ENVIRONMENT_TYPES_LABELED.map(({ value, label, icon }) => (
                <MenuItem key={label} value={value} sx={{ display: 'flex', alignItems: 'center' }}>
                  <Stack spacing={1} direction="row">
                    <Iconify icon={icon} />

                    <Typography variant="body2">{label}</Typography>
                  </Stack>
                </MenuItem>
              ))}
            </RHFSelect>

            {selectedEnvironment === EnvironmentTypes.WAREHOUSE && (
              <RHFSelect name="environmentId" label="Armazém">
                {warehouses?.map(({ companyName, warehouses: companyWarehouses }) => [
                  <InputLabel
                    key={`${companyName}-header`}
                    sx={{ color: 'text.secondary', fontStyle: 'italic', padding: 0.5 }}
                  >
                    {companyName}
                  </InputLabel>,
                  companyWarehouses.map((warehouse) => (
                    <MenuItem
                      key={warehouse.id}
                      value={warehouse.id}
                      sx={{ display: 'flex', alignItems: 'center' }}
                    >
                      <Stack spacing={1} direction="row">
                        <Iconify icon="mdi:warehouse" />
                        <Typography variant="body2">{warehouse.name}</Typography>
                      </Stack>
                    </MenuItem>
                  )),
                ])}
              </RHFSelect>
            )}

            {selectedEnvironment === EnvironmentTypes.COMPANY && (
              <RHFSelect name="environmentId" label="Empresa">
                {companies?.map((company) => (
                  <MenuItem
                    key={company.id}
                    value={company.id}
                    sx={{ display: 'flex', alignItems: 'center' }}
                  >
                    <Stack spacing={1} direction="row">
                      <Iconify icon="vaadin:office" />
                      <Typography variant="body2">{company.name}</Typography>
                    </Stack>
                  </MenuItem>
                ))}
              </RHFSelect>
            )}
          </Box>

          <Box
            rowGap={3}
            columnGap={2}
            paddingTop={1}
            display="grid"
            gridTemplateColumns={{
              xs: 'repeat(2, 1fr)',
              sm: 'repeat(3, 1fr)',
            }}
          >
            {Object.entries(currentAccount.role).map(([key, value]) => (
              <RHFSwitch key={key} label={key} name={`role.${key}`} checked={value} />
            ))}
          </Box>
        </DialogContent>

        <DialogActions>
          <Button variant="outlined" onClick={onClose}>
            Cancelar
          </Button>

          <LoadingButton type="submit" variant="contained" loading={isSubmitting || isPending}>
            Alterar
          </LoadingButton>
        </DialogActions>
      </FormProvider>
    </Dialog>
  );
}
