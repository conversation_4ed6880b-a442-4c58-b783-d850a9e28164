import { useForm } from 'react-hook-form';
import { useMemo, useEffect } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';

import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import { Box, Grid, InputLabel } from '@mui/material';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import Dialog, { DialogProps } from '@mui/material/Dialog';

import { useAuthContext } from 'src/auth/hooks';
import { EnvironmentTypes } from 'src/enum/environment';
import { roleDefaultAtributes } from 'src/consts/roles';
import { CreateAccountSchema } from 'src/schemas/account';
import { useAccountCreateMutation } from 'src/mutations/account';
import { ENVIRONMENT_TYPES_LABELED } from 'src/consts/environment';
import { useCompanyListQuery } from 'src/queries/company/company-query';
import { useTenantListWarehouseQuery } from 'src/queries/tenant/tenant-query';

import Iconify from 'src/components/iconify';
import FormProvider, { RHFSelect, RHFSwitch, RHFTextField } from 'src/components/hook-form';

import { IAccountNewForm } from 'src/types/account';

// ----------------------------------------------------------------------

type Props = {
  open: boolean;
  onClose: VoidFunction;
} & DialogProps;

export default function AccountQuickNewForm({ open, onClose, ...rest }: Props) {
  const { account } = useAuthContext();
  const tenantId = account?.tenantId!;

  const { data: companies } = useCompanyListQuery();

  const { data: warehouses } = useTenantListWarehouseQuery(tenantId);

  const defaultValues: IAccountNewForm = useMemo(
    () => ({
      name: '',
      email: '',
      password: '',
      role: roleDefaultAtributes,
      environment: '',
      environmentId: tenantId,
    }),
    [tenantId]
  );

  const methods = useForm<IAccountNewForm>({
    resolver: yupResolver<IAccountNewForm>(CreateAccountSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
    watch,
    setValue,
  } = methods;

  const { mutateAsync, isPending } = useAccountCreateMutation({
    callbackFn: () => {
      reset();
      onClose();
    },
  });

  const onSubmit = handleSubmit(async (newAccount) => {
    await mutateAsync({
      newAccount,
    });
  });

  const selectedEnvironment = watch('environment');

  useEffect(() => {
    if (selectedEnvironment === EnvironmentTypes.TENANT) {
      setValue('environmentId', tenantId);
    } else {
      setValue('environmentId', '');
    }
  }, [selectedEnvironment, setValue, tenantId]);

  return (
    <Dialog
      fullWidth
      maxWidth={false}
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { maxWidth: 720 },
      }}
      {...rest}
    >
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <DialogTitle>Criar usuário</DialogTitle>

        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <RHFTextField name="name" label="Nome completo" placeholder="Fulano da Silva" />
            </Grid>

            <Grid item xs={6}>
              <RHFTextField name="email" label="Endereço de email" placeholder="<EMAIL>" />
            </Grid>

            <Grid item xs={12}>
              <RHFTextField name="password" label="Senha" type="password" />
            </Grid>

            <Grid item xs={6}>
              <RHFSelect name="environment" label="Environment">
                {ENVIRONMENT_TYPES_LABELED.map(({ value, label, icon }) => (
                  <MenuItem
                    key={label}
                    value={value}
                    sx={{ display: 'flex', alignItems: 'center' }}
                  >
                    <Stack spacing={1} direction="row">
                      <Iconify icon={icon} />

                      <Typography variant="body2">{label}</Typography>
                    </Stack>
                  </MenuItem>
                ))}
              </RHFSelect>
            </Grid>

            {selectedEnvironment === EnvironmentTypes.WAREHOUSE && (
              <Grid item xs={6}>
                <RHFSelect name="environmentId" label="Armazém">
                  {warehouses?.map(({ companyName, warehouses: companyWarehouses }) => [
                    <InputLabel
                      key={`${companyName}-header`}
                      sx={{ color: 'text.secondary', fontStyle: 'italic', padding: 0.5 }}
                    >
                      {companyName}
                    </InputLabel>,
                    companyWarehouses.map((warehouse) => (
                      <MenuItem
                        key={warehouse.id}
                        value={warehouse.id}
                        sx={{ display: 'flex', alignItems: 'center' }}
                      >
                        <Stack spacing={1} direction="row">
                          <Iconify icon="mdi:warehouse" />
                          <Typography variant="body2">{warehouse.name}</Typography>
                        </Stack>
                      </MenuItem>
                    )),
                  ])}
                </RHFSelect>
              </Grid>
            )}

            {selectedEnvironment === EnvironmentTypes.COMPANY && (
              <Grid item xs={6}>
                <RHFSelect name="environmentId" label="Empresa">
                  {companies?.map((company) => (
                    <MenuItem
                      key={company.id}
                      value={company.id}
                      sx={{ display: 'flex', alignItems: 'center' }}
                    >
                      <Stack spacing={1} direction="row">
                        <Iconify icon="vaadin:office" />
                        <Typography variant="body2">{company.name}</Typography>
                      </Stack>
                    </MenuItem>
                  ))}
                </RHFSelect>
              </Grid>
            )}
          </Grid>

          <Box
            rowGap={3}
            columnGap={2}
            paddingTop={1}
            display="grid"
            gridTemplateColumns={{
              xs: 'repeat(2, 1fr)',
              sm: 'repeat(3, 1fr)',
            }}
          >
            {Object.entries(roleDefaultAtributes).map(([key, value]) => (
              <RHFSwitch key={key} label={key} name={`role.${key}`} checked={value} />
            ))}
          </Box>
        </DialogContent>

        <DialogActions>
          <Button variant="outlined" onClick={onClose}>
            Cancelar
          </Button>

          <LoadingButton type="submit" variant="contained" loading={isSubmitting || isPending}>
            Criar
          </LoadingButton>
        </DialogActions>
      </FormProvider>
    </Dialog>
  );
}
