import Stack from '@mui/material/Stack';

import CustomPopover, { usePopover } from 'src/components/custom-popover';

import { IAccountTableFilters, IAccountTableFilterValue } from 'src/types/account';

// ----------------------------------------------------------------------

type Props = {
  filters: IAccountTableFilters;
  onFilters: (name: string, value: IAccountTableFilterValue) => void;
  //
  roleOptions: string[];
};

export default function AccountTableToolbar({
  filters,
  onFilters,
  //
  roleOptions,
}: Props) {
  const popover = usePopover();

  return (
    <>
      <Stack
        spacing={2}
        alignItems={{ xs: 'flex-end', md: 'center' }}
        direction={{
          xs: 'column',
          md: 'row',
        }}
        sx={{
          p: 2.5,
          pr: { xs: 2.5, md: 1 },
        }}
      />

      <CustomPopover
        open={popover.open}
        onClose={popover.onClose}
        arrow="right-top"
        sx={{ width: 140 }}
      />
    </>
  );
}
