import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import Container from '@mui/material/Container';
import Accordion from '@mui/material/Accordion';
import CardHeader from '@mui/material/CardHeader';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { fMaskCNPJ } from 'src/utils/format-mask';

import { useCompanyShowSuspenseQuery } from 'src/queries/company/company-query';

import Iconify from 'src/components/iconify';
import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';

import CompanyTypeLabel from './company-type-label';
import WarehouseTableList from '../warehouse/table/warehouse-table-list';

// ------------------------------------------------------------

type ComponentProps = {
  companyId: string;
};

export default function CompanyDetails({ companyId }: ComponentProps) {
  const settings = useSettingsContext();

  const { data: company } = useCompanyShowSuspenseQuery({ companyId });

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <CustomBreadcrumbs
        heading={company.name}
        links={[
          { name: 'Dashboard', href: paths.dashboard.root },
          { name: 'Filiais/matriz', href: paths.dashboard.company.root },
          { name: `${company.name}` },
        ]}
        action={
          <Stack direction="row" spacing={2}>
            <Tooltip title="Editar filial/matriz" placement="top">
              <Button
                color="warning"
                variant="contained"
                LinkComponent={RouterLink}
                href={paths.dashboard.company.edit(companyId)}
              >
                <Iconify icon="material-symbols:edit" />
              </Button>
            </Tooltip>
          </Stack>
        }
        sx={{ mb: 3 }}
      />

      <Stack spacing={3}>
        <Card>
          <CardHeader title={company.name} />

          <CardContent>
            <Grid container spacing={2} sx={{ rowGap: 3 }}>
              <Grid item xs={12} sm={6} md={4} lg={3}>
                <Typography paragraph variant="overline" sx={{ color: 'text.disabled' }}>
                  Nome
                </Typography>
                <Typography variant="body2">{company.name}</Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4} lg={3}>
                <Typography paragraph variant="overline" sx={{ color: 'text.disabled' }}>
                  Tipo de empresa
                </Typography>
                <Typography variant="body2">
                  <CompanyTypeLabel companyType={company.companyType} />
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4} lg={3}>
                <Typography paragraph variant="overline" sx={{ color: 'text.disabled' }}>
                  CNPJ
                </Typography>
                <Typography variant="body2">{fMaskCNPJ(company.cnpj)}</Typography>
              </Grid>

              <Grid item xs={12}>
                <Accordion>
                  <AccordionSummary
                    id="warehouses-header"
                    aria-controls="warehouses-content"
                    expandIcon={<ExpandMoreIcon />}
                  >
                    <Stack spacing={1} direction="row">
                      <Iconify icon="material-symbols:warehouse" />

                      <Typography variant="button">Armazéns</Typography>
                    </Stack>
                  </AccordionSummary>

                  <AccordionDetails>
                    <WarehouseTableList companyId={companyId} />
                  </AccordionDetails>
                </Accordion>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Stack>
    </Container>
  );
}
