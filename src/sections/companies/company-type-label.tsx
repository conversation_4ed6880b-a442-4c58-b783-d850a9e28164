import { CompanyTypes } from 'src/enum/company';

import Label from 'src/components/label';

type CompanyTypeLabelProps = {
  companyType: CompanyTypes | null;
};

export default function CompanyTypeLabel({ companyType }: CompanyTypeLabelProps) {
  switch (companyType) {
    case CompanyTypes.FILIAL:
      return <Label color="success">Filial</Label>;

    case CompanyTypes.MATRIZ:
      return <Label color="warning">Matriz</Label>;

    default:
      <Label color="default">N/A</Label>;
  }
}
