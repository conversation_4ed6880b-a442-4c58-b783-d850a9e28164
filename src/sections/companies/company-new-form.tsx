'use client';

import { useMemo } from 'react';
import { isEqual } from 'lodash';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { yupResolver } from '@hookform/resolvers/yup';

import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Grid from '@mui/system/Unstable_Grid';
import MenuItem from '@mui/material/MenuItem';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import CardHeader from '@mui/material/CardHeader';
import LoadingButton from '@mui/lab/LoadingButton';
import CardContent from '@mui/material/CardContent';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { useResponsive } from 'src/hooks/use-responsive';

import { CompanyTypes } from 'src/enum/company';
import { NewCompanySchema } from 'src/schemas/company';
import { COMPANY_TYPES_LABELED } from 'src/consts/company';
import { useCompanyCreateMutation } from 'src/mutations/company';

import Iconify from 'src/components/iconify';
import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';
import FormProvider from 'src/components/hook-form/form-provider';
import { RHFCNPJ, RHFSelect, RHFTextField } from 'src/components/hook-form';

import { ICompanyData, ICompanyNewForm } from 'src/types/company';

export default function CompanyNewForm() {
  const { push } = useRouter();

  const settings = useSettingsContext();

  const mdUp = useResponsive('up', 'md');

  const defaultValues: ICompanyNewForm = useMemo(
    () => ({
      name: '',
      cnpj: '',
      companyType: CompanyTypes.FILIAL,
    }),
    []
  );

  const methods = useForm<ICompanyNewForm>({
    resolver: yupResolver<ICompanyNewForm>(NewCompanySchema),
    defaultValues,
  });

  const {
    watch,
    handleSubmit,
    formState: { isLoading },
  } = methods;

  const formValues = watch();

  const newCompanyMutation = useCompanyCreateMutation({
    callbackFn: () => {
      // reset();

      push(paths.dashboard.company.root);
    },
  });

  const onSubmit = handleSubmit(async (data) => {
    const newCompany: ICompanyData = {
      name: data.name || '',
      cnpj: data.cnpj || undefined,
      companyType: data.companyType,
    };

    await newCompanyMutation.mutateAsync({ newCompany });
  });

  const canSave = isEqual(defaultValues, formValues);

  return (
    <FormProvider methods={methods} onSubmit={onSubmit}>
      <Container maxWidth={settings.themeStretch ? false : 'xl'}>
        <CustomBreadcrumbs
          heading="Nova filial/matriz"
          links={[
            { name: 'Dashboard', href: paths.dashboard.root },
            { name: 'Filiais', href: paths.dashboard.company.root },
            { name: 'Nova filial' },
          ]}
          sx={{ mb: 3 }}
        />

        <Grid container spacing={3}>
          {mdUp && (
            <Grid md={3}>
              <Typography variant="h6" sx={{ mb: 0.5 }}>
                Criar filial/matriz
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Dados da filial/matriz.
              </Typography>
            </Grid>
          )}

          <Grid xs={12} md={9}>
            <Card>
              {!mdUp && <CardHeader title="Criar filial/matriz" />}

              <CardContent>
                <Stack spacing={2}>
                  <RHFTextField name="name" label="Nome" />
                  <RHFCNPJ name="cnpj" label="CNPJ da matriz" />

                  <RHFSelect label="Tipo de empresa" name="companyType">
                    {COMPANY_TYPES_LABELED.map(({ value, label, icon }) => (
                      <MenuItem
                        key={label}
                        value={value}
                        sx={{ display: 'flex', alignItems: 'center' }}
                      >
                        <Stack spacing={1} direction="row">
                          <Iconify icon={icon} />

                          <Typography variant="body2">{label}</Typography>
                        </Stack>
                      </MenuItem>
                    ))}
                  </RHFSelect>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          {mdUp && <Grid md={4} />}

          <Grid
            xs={12}
            md={8}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-end',
            }}
          >
            <Button
              variant="contained"
              size="large"
              color="error"
              component={RouterLink}
              href={paths.dashboard.company.root}
              sx={{ marginRight: 3 }}
            >
              Cancelar
            </Button>

            <LoadingButton
              size="large"
              type="submit"
              variant="contained"
              disabled={canSave}
              loading={newCompanyMutation.isPending || isLoading}
            >
              Criar filial/matriz
            </LoadingButton>
          </Grid>
        </Grid>
      </Container>
    </FormProvider>
  );
}
