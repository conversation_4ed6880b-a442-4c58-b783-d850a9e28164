'use client';

import isEqual from 'lodash/isEqual';
import { useState, useEffect, useCallback } from 'react';

// @mui
import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';

// routes
import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

// hooks

import { useCompanyDeleteMutation } from 'src/mutations/company';
import { useCompanyListSuspenseQuery } from 'src/queries/company/company-query';

// components
import Iconify from 'src/components/iconify';
import Scrollbar from 'src/components/scrollbar';
import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';
import {
  useTable,
  emptyRows,
  TableNoData,
  getComparator,
  TableEmptyRows,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

// types
import { ICompany, ICompanyTableFilterValue } from 'src/types/company';

import CompanyTableRow from './company-table-row';
import CompanyTableToolbar from './company-table-toolbar';
import CompanyTableFiltersResult from './company-table-filters-result';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: 'cnpj', label: 'CNPJ' },
  { id: 'name', label: 'Nome' },
  { id: 'type', label: 'Tipo de empresa' },
  { id: 'createdAt', label: 'Criada em' },
  { id: 'updatedAt', label: 'Última alteração em' },
  { id: '', width: 88 },
];

const defaultFilters = {
  name: '',
};

// ----------------------------------------------------------------------

export default function CompanyTableList() {
  const settings = useSettingsContext();

  const table = useTable();

  const { data: companies } = useCompanyListSuspenseQuery();

  const [filters, setFilters] = useState(defaultFilters);

  const [tableData, setTableData] = useState<ICompany[]>(companies ?? []);

  const dataFiltered = applyFilter({
    inputData: tableData,
    comparator: getComparator(table.order, table.orderBy),
    filters,
  });

  const denseHeight = table.dense ? 52 : 72;

  const canReset = !isEqual(defaultFilters, filters);

  const notFound = (!dataFiltered.length && canReset) || !dataFiltered.length;

  const handleFilters = useCallback(
    (name: string, value: ICompanyTableFilterValue) => {
      table.onResetPage();
      setFilters((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    },
    [table]
  );

  const companyDeleteMutation = useCompanyDeleteMutation();

  const handleDeleteCompany = async (companyId: string) => {
    companyDeleteMutation.mutateAsync({ companyId });
  };

  const handleResetFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);

  useEffect(() => {
    setTableData(companies);
  }, [companies]);

  return (
    <Container maxWidth={settings.themeStretch ? false : 'lg'}>
      <CustomBreadcrumbs
        heading="Lista de Filiais"
        links={[{ name: 'Início', href: paths.dashboard.root }, { name: 'Lista' }]}
        action={
          <Button
            variant="contained"
            component={RouterLink}
            href={paths.dashboard.company.new}
            startIcon={<Iconify icon="mingcute:add-line" />}
          >
            Adicionar Filial
          </Button>
        }
        sx={{
          mb: { xs: 3, md: 5 },
        }}
      />

      <Card>
        <CompanyTableToolbar filters={filters} onFilters={handleFilters} />

        {canReset && (
          <CompanyTableFiltersResult
            filters={filters}
            onFilters={handleFilters}
            //
            onResetFilters={handleResetFilters}
            //
            results={dataFiltered.length}
            sx={{ p: 2.5, pt: 0 }}
          />
        )}

        <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
          <Scrollbar>
            <Table size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
              <TableHeadCustom
                order={table.order}
                orderBy={table.orderBy}
                headLabel={TABLE_HEAD}
                onSort={table.onSort}
              />

              <TableBody>
                {dataFiltered
                  ?.slice(
                    table.page * table.rowsPerPage,
                    table.page * table.rowsPerPage + table.rowsPerPage
                  )
                  .map((row) => (
                    <CompanyTableRow
                      row={row}
                      key={row.id}
                      onDeleteRow={() => handleDeleteCompany(row.id)}
                    />
                  ))}

                <TableEmptyRows
                  height={denseHeight}
                  emptyRows={emptyRows(table.page, table.rowsPerPage, tableData.length)}
                />

                <TableNoData notFound={notFound} />
              </TableBody>
            </Table>
          </Scrollbar>
        </TableContainer>

        <TablePaginationCustom
          totalPages={Math.floor(dataFiltered.length / table.rowsPerPage)}
          count={tableData.length}
          page={table.page}
          rowsPerPage={table.rowsPerPage}
          onPageChange={table.onChangePage}
          onRowsPerPageChange={table.onChangeRowsPerPage}
          //
          dense={table.dense}
          onChangeDense={table.onChangeDense}
        />
      </Card>
    </Container>
  );
}

// ----------------------------------------------------------------------

function applyFilter({
  inputData,
  comparator,
  filters,
}: {
  inputData: ICompany[];
  comparator: (a: any, b: any) => number;
  filters: typeof defaultFilters;
}) {
  const { name } = filters;

  const stabilizedThis = inputData?.map((el, index) => [el, index] as const);

  stabilizedThis?.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });

  inputData = stabilizedThis.map((el) => el[0]);

  if (name) {
    inputData = inputData.filter(
      (company) => company.name.toLowerCase().indexOf(name.toLowerCase()) !== -1
    );
  }

  return inputData;
}
