// @mui
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import IconButton from '@mui/material/IconButton';
import ListItemText from '@mui/material/ListItemText';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

// hooks
import { useBoolean } from 'src/hooks/use-boolean';

import { fDateTime } from 'src/utils/format-time';
import { fMaskCNPJ } from 'src/utils/format-mask';

// components
import Iconify from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';

//
import { ICompany } from 'src/types/company';

import CompanyTypeLabel from '../company-type-label';

// ----------------------------------------------------------------------

type Props = {
  row: ICompany;
  onDeleteRow: VoidFunction;
};

export default function CompanyTableRow({ row, onDeleteRow }: Props) {
  const { id, name, cnpj, companyType, createdAt, updatedAt } = row;

  const confirm = useBoolean();

  return (
    <>
      <TableRow hover>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>{fMaskCNPJ(cnpj)}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>
          <ListItemText
            primary={name}
            primaryTypographyProps={{ typography: 'body2' }}
            secondaryTypographyProps={{
              component: 'span',
              color: 'text.disabled',
            }}
          />
        </TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}> 
          <CompanyTypeLabel companyType={companyType} />
        </TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{fDateTime(createdAt)}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{fDateTime(updatedAt)}</TableCell>

        <TableCell align="right" sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title="Ver detalhes da filial" placement="top" arrow>
            <IconButton color="info" component={RouterLink} href={paths.dashboard.company.show(id)}>
              <Iconify icon="mdi:eye" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Editar filial" placement="top" arrow>
            <IconButton
              color="warning"
              component={RouterLink}
              href={paths.dashboard.company.edit(id)}
            >
              <Iconify icon="solar:pen-bold" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Excluir filial" placement="top" arrow>
            <IconButton color="error" onClick={confirm.onTrue}>
              <Iconify icon="solar:trash-bin-trash-bold" />
            </IconButton>
          </Tooltip>
        </TableCell>
      </TableRow>

      <ConfirmDialog
        open={confirm.value}
        onClose={confirm.onFalse}
        title="Excluir"
        content="Tem certeza que deseja excluir esta filial? Essa ação não poderá ser desfeita."
        action={
          <Button variant="contained" color="error" onClick={onDeleteRow}>
            Excluir
          </Button>
        }
      />
    </>
  );
}
