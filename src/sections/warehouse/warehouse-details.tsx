import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import Container from '@mui/material/Container';
import CardHeader from '@mui/material/CardHeader';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { useWarehouseShowSuspenseQuery } from 'src/queries/warehouse';

import Iconify from 'src/components/iconify';
import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';

// ------------------------------------------------------------

type ComponentProps = {
  companyId: string;
  warehouseId: string;
};

export default function WarehouseDetails({ companyId, warehouseId }: ComponentProps) {
  const settings = useSettingsContext();

  const { data: warehouse } = useWarehouseShowSuspenseQuery({ companyId, warehouseId });

  return (
    <Container maxWidth={settings.themeStretch ? false : 'xl'}>
      <CustomBreadcrumbs
        heading={warehouse.name}
        links={[
          { name: 'Dashboard', href: paths.dashboard.root },
          { name: 'Armazéns', href: paths.dashboard.warehouse.root(companyId) },
          { name: `${warehouse.name}` },
        ]}
        action={
          <Stack direction="row" spacing={2}>
            <Tooltip title="Editar armazém" placement="top">
              <Button
                color="warning"
                variant="contained"
                LinkComponent={RouterLink}
                href={paths.dashboard.warehouse.edit(companyId, warehouseId)}
              >
                <Iconify icon="material-symbols:edit" />
              </Button>
            </Tooltip>
          </Stack>
        }
        sx={{ mb: 3 }}
      />

      <Card>
        <CardHeader title={warehouse.name} />

        <CardContent>
          <Grid container spacing={2} sx={{ rowGap: 3 }}>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Typography paragraph variant="overline" sx={{ color: 'text.disabled' }}>
                Nome
              </Typography>
              <Typography variant="body2">{warehouse.name}</Typography>
            </Grid>

            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Typography paragraph variant="overline" sx={{ color: 'text.disabled' }}>
                Largura
              </Typography>
              <Typography variant="body2">{warehouse.length}</Typography>
            </Grid>

            <Grid item xs={12} sm={6} md={4} lg={3}>
              <Typography paragraph variant="overline" sx={{ color: 'text.disabled' }}>
                Comprimento
              </Typography>
              <Typography variant="body2">{warehouse.width}</Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
