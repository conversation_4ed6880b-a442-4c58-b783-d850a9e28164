'use client';

import { useMemo } from 'react';
import { isEqual } from 'lodash';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { yupResolver } from '@hookform/resolvers/yup';

import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import Container from '@mui/material/Container';
import CardHeader from '@mui/material/CardHeader';
import LoadingButton from '@mui/lab/LoadingButton';
import CardContent from '@mui/material/CardContent';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { useResponsive } from 'src/hooks/use-responsive';

import { UpdateWarehouseSchema } from 'src/schemas/warehouse';
import { useWarehouseShowSuspenseQuery } from 'src/queries/warehouse';
import { useWarehouseUpdateMutation } from 'src/mutations/warehouse/warehouse-mutation';

import Iconify from 'src/components/iconify';
import { RHFTextField } from 'src/components/hook-form';
import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';
import FormProvider from 'src/components/hook-form/form-provider';

import { IWarehouseData, IWarehouseEditForm } from 'src/types/warehouse';

// ----------------------------------------------------------

type ComponentProps = {
  companyId: string;
  warehouseId: string;
};

export default function WarehouseEditForm({ companyId, warehouseId }: ComponentProps) {
  const { push } = useRouter();

  const settings = useSettingsContext();

  const mdUp = useResponsive('up', 'md');

  const { data: currentWarehouse } = useWarehouseShowSuspenseQuery({
    companyId,
    warehouseId,
  });

  const defaultValues: IWarehouseEditForm = useMemo(
    () => ({
      name: currentWarehouse.name ?? '',
      width: currentWarehouse.width ?? 0,
      length: currentWarehouse.length ?? 0,
    }),
    [currentWarehouse]
  );

  const methods = useForm<IWarehouseEditForm>({
    resolver: yupResolver<IWarehouseEditForm>(UpdateWarehouseSchema),
    defaultValues,
  });

  const {
    watch,
    handleSubmit,
    formState: { isLoading },
  } = methods;

  const formValues = watch();

  const updateWarehouseMutation = useWarehouseUpdateMutation({
    callbackFn: () => {
      // reset();

      push(paths.dashboard.warehouse.root(companyId));
    },
  });

  const onSubmit = handleSubmit(async (data) => {
    const updatedWarehouse: Partial<IWarehouseData> = {
      name: data.name || undefined,
      width: data.width || undefined,
      length: data.length || undefined,
    };

    await updateWarehouseMutation.mutateAsync({
      companyId,
      warehouseId,
      updatedWarehouse,
    });
  });

  const canSave = isEqual(defaultValues, formValues);

  return (
    <FormProvider methods={methods} onSubmit={onSubmit}>
      <Container maxWidth={settings.themeStretch ? false : 'lg'}>
        <CustomBreadcrumbs
          heading="Alterar armazém"
          links={[
            { name: 'Dashboard', href: paths.dashboard.root },
            { name: 'Armazéns', href: paths.dashboard.warehouse.root(companyId) },
            { name: `${currentWarehouse.name}` },
          ]}
          action={
            <Tooltip title="Ver armazém" placement="top">
              <Button
                color="info"
                variant="contained"
                LinkComponent={RouterLink}
                href={paths.dashboard.warehouse.show(companyId, warehouseId)}
              >
                <Iconify icon="mdi:eye" />
              </Button>
            </Tooltip>
          }
          sx={{ mb: 3 }}
        />

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              {!mdUp && <CardHeader title="Alterar armazém" />}

              <CardContent>
                <Stack spacing={2}>
                  <RHFTextField name="name" label="Nome do armazém" />

                  <RHFTextField name="width" label="Comprimento" />

                  <RHFTextField name="length" label="Largura" />
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid
            item
            xs={12}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-end',
            }}
          >
            <Button
              variant="contained"
              size="large"
              color="error"
              component={RouterLink}
              href={paths.dashboard.warehouse.root(companyId)}
              sx={{ marginRight: 3 }}
            >
              Cancelar
            </Button>

            <LoadingButton
              size="large"
              type="submit"
              variant="contained"
              disabled={canSave}
              loading={updateWarehouseMutation.isPending || isLoading}
            >
              Alterar armazém
            </LoadingButton>
          </Grid>
        </Grid>
      </Container>
    </FormProvider>
  );
}
