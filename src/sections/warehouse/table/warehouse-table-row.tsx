// @mui
import { useParams } from 'next/navigation';

import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import IconButton from '@mui/material/IconButton';
import ListItemText from '@mui/material/ListItemText';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

// hooks
import { useBoolean } from 'src/hooks/use-boolean';

import { fDateTime } from 'src/utils/format-time';

// components
import Iconify from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';

//
import { IWarehouse } from 'src/types/warehouse';

// ----------------------------------------------------------------------

type Props = {
  row: IWarehouse;
  onDeleteRow: VoidFunction;
};

export default function WarehouseTableRow({ row, onDeleteRow }: Props) {
  const params = useParams<{ companyId: string }>();
  const { companyId } = params;

  const { id, name, width, length, createdAt, updatedAt } = row;

  const confirm = useBoolean();

  return (
    <>
      <TableRow hover>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>{name}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>
          <ListItemText
            primary={`Largura: ${length}`}
            secondary={`Comprimento: ${width}`}
            primaryTypographyProps={{ typography: 'body2' }}
            secondaryTypographyProps={{
              component: 'span',
              color: 'text.disabled',
            }}
          />
        </TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{fDateTime(createdAt)}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{fDateTime(updatedAt)}</TableCell>

        <TableCell align="right" sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title="Ver detalhes da armazém" placement="top" arrow>
            <IconButton
              color="info"
              component={RouterLink}
              href={paths.dashboard.warehouse.show(companyId, id)}
            >
              <Iconify icon="mdi:eye" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Editar armazém" placement="top" arrow>
            <IconButton
              color="warning"
              component={RouterLink}
              href={paths.dashboard.warehouse.edit(companyId, id)}
            >
              <Iconify icon="solar:pen-bold" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Excluir armazém" placement="top" arrow>
            <IconButton color="error" onClick={confirm.onTrue}>
              <Iconify icon="solar:trash-bin-trash-bold" />
            </IconButton>
          </Tooltip>
        </TableCell>
      </TableRow>

      <ConfirmDialog
        open={confirm.value}
        onClose={confirm.onFalse}
        title="Excluir"
        content="Tem certeza que deseja excluir este armazém? Essa ação não poderá ser desfeita."
        action={
          <Button variant="contained" color="error" onClick={onDeleteRow}>
            Excluir
          </Button>
        }
      />
    </>
  );
}
