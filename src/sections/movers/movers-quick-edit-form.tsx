import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { MoverType } from 'src/enum/mover';
import { MoverSchema } from 'src/schemas/mover';
import { useCompanyContext } from 'src/company/hooks';
import { MOVER_TYPES_LABELED } from 'src/consts/mover';
import { useMoverUpdateMutation } from 'src/mutations/mover/mover-mutation';

import Iconify from 'src/components/iconify';
import FormProvider, { RHFSelect, RHFTextField } from 'src/components/hook-form';

import { IMover } from 'src/types/mover';

// ----------------------------------------------------------------------

type Props = {
  open: boolean;
  onClose: VoidFunction;
  currentMover: IMover;
};

export default function MoverQuickEditForm({ currentMover, open, onClose }: Props) {
  const defaultValues = useMemo(
    () => ({
      height: currentMover?.height || 0,
      length: currentMover?.length || 0,
      type: currentMover?.type || MoverType.STOCK,
      weightCapacity: currentMover?.weightCapacity || 0,
      width: currentMover?.width || 0,
      warehouseId: currentMover?.warehouseId || '',
    }),
    [currentMover]
  );

  const { currentCompany } = useCompanyContext();

  const methods = useForm({
    resolver: yupResolver(MoverSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const { mutateAsync, isPending } = useMoverUpdateMutation({
    callbackFn: () => {
      reset();
      onClose();
    },
  });

  const onSubmit = handleSubmit(async (updatedMover) => {
    mutateAsync({
      companyId: currentCompany?.id!,
      warehouseId: currentMover.warehouseId,
      moverId: currentMover.id,
      updatedMover,
    });
  });

  return (
    <Dialog
      fullWidth
      maxWidth={false}
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { maxWidth: 720 },
      }}
    >
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <DialogTitle>Alterar movimentador</DialogTitle>

        <DialogContent>
          <Box
            rowGap={3}
            columnGap={2}
            paddingTop={1}
            display="grid"
            gridTemplateColumns={{
              xs: 'repeat(1, 1fr)',
              sm: 'repeat(2, 1fr)',
            }}
          >
            <RHFSelect name="type" label="Tipo">
              {MOVER_TYPES_LABELED.map(({ value, label, icon }) => (
                <MenuItem key={label} value={value} sx={{ display: 'flex', alignItems: 'center' }}>
                  <Stack spacing={1} direction="row">
                    <Iconify icon={icon} />

                    <Typography variant="body2">{label}</Typography>
                  </Stack>
                </MenuItem>
              ))}
            </RHFSelect>
            <RHFTextField name="width" label="Largura" placeholder="10" type="number" />
            <RHFTextField name="length" label="Comprimento" placeholder="10" type="number" />
            <RHFTextField name="height" label="Altura" placeholder="10" type="number" />
            <RHFTextField
              name="weightCapacity"
              label="Capacidade de carga"
              placeholder="10"
              type="number"
            />
          </Box>
        </DialogContent>

        <DialogActions>
          <Button variant="outlined" onClick={onClose}>
            Cancelar
          </Button>

          <LoadingButton type="submit" variant="contained" loading={isSubmitting || isPending}>
            Alterar
          </LoadingButton>
        </DialogActions>
      </FormProvider>
    </Dialog>
  );
}
