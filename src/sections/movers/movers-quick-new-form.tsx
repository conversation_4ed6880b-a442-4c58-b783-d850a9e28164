import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import Dialog, { DialogProps } from '@mui/material/Dialog';

import { MoverType } from 'src/enum/mover';
import { MoverSchema } from 'src/schemas/mover';
import { useCompanyContext } from 'src/company/hooks';
import { MOVER_TYPES_LABELED } from 'src/consts/mover';
import { useMoverCreateMutation } from 'src/mutations/mover';

import Iconify from 'src/components/iconify';
import FormProvider, { RHFSelect, RHFTextField } from 'src/components/hook-form';

import { IMoverNewForm } from 'src/types/mover';

// ----------------------------------------------------------------------

type Props = {
  open: boolean;
  onClose: VoidFunction;
} & DialogProps;

export default function MoverQuickNewForm({ open, onClose, ...rest }: Props) {
  const { currentCompany } = useCompanyContext();

  const defaultValues: IMoverNewForm = useMemo(
    () => ({
      height: 0,
      length: 0,
      type: MoverType.STOCK,
      weightCapacity: 0,
      width: 0,
      companyId: currentCompany?.id || '',
    }),
    [currentCompany]
  );

  const methods = useForm<IMoverNewForm>({
    resolver: yupResolver<IMoverNewForm>(MoverSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const { mutateAsync, isPending } = useMoverCreateMutation({
    callbackFn: () => {
      reset();
      onClose();
    },
  });

  const onSubmit = handleSubmit(async (newMover) => {
    mutateAsync({ newMover, companyId: currentCompany?.id!, warehouseId: newMover.warehouseId! });
  });

  return (
    <Dialog
      fullWidth
      maxWidth={false}
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { maxWidth: 720 },
      }}
      {...rest}
    >
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <DialogTitle>Criar movimentador</DialogTitle>

        <DialogContent>
          <Box
            rowGap={3}
            columnGap={2}
            paddingTop={1}
            display="grid"
            gridTemplateColumns={{
              xs: 'repeat(1, 1fr)',
              sm: 'repeat(2, 1fr)',
            }}
          >
            <RHFSelect name="type" label="Tipo">
              {MOVER_TYPES_LABELED.map(({ value, label, icon }) => (
                <MenuItem key={label} value={value} sx={{ display: 'flex', alignItems: 'center' }}>
                  <Stack spacing={1} direction="row">
                    <Iconify icon={icon} />

                    <Typography variant="body2">{label}</Typography>
                  </Stack>
                </MenuItem>
              ))}
            </RHFSelect>
            <RHFTextField name="width" label="Largura" placeholder="10" type="number" />
            <RHFTextField name="length" label="Comprimento" placeholder="10" type="number" />
            <RHFTextField name="height" label="Altura" placeholder="10" type="number" />
            <RHFTextField
              name="weightCapacity"
              label="Capacidade de carga"
              placeholder="10"
              type="number"
            />
          </Box>
        </DialogContent>

        <DialogActions>
          <Button variant="outlined" onClick={onClose}>
            Cancelar
          </Button>

          <LoadingButton type="submit" variant="contained" loading={isSubmitting || isPending}>
            Criar
          </LoadingButton>
        </DialogActions>
      </FormProvider>
    </Dialog>
  );
}
