import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import MenuItem from '@mui/material/MenuItem';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import IconButton from '@mui/material/IconButton';
import ListItemText from '@mui/material/ListItemText';

import { useBoolean } from 'src/hooks/use-boolean';

import { fDateTime } from 'src/utils/format-time';

import Iconify from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';
import CustomPopover, { usePopover } from 'src/components/custom-popover';

import { IMover } from 'src/types/mover';

import MoverQuickEditForm from './movers-quick-edit-form';

// ----------------------------------------------------------------------

type Props = {
  row: IMover;
  onDeleteRow: VoidFunction;
};

export default function MoverTableRow({ row, onDeleteRow }: Props) {
  const { name, type, width, height, length, weightCapacity, createdAt, updatedAt } = row;

  const confirm = useBoolean();

  const quickEdit = useBoolean();

  const popover = usePopover();

  return (
    <>
      <TableRow hover>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>
          <ListItemText
            primary={name}
            primaryTypographyProps={{ typography: 'body2' }}
            secondaryTypographyProps={{
              component: 'span',
              color: 'text.disabled',
            }}
          />
        </TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{type}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{width}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{length}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{height}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{weightCapacity}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{fDateTime(createdAt)}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{fDateTime(updatedAt)}</TableCell>

        <TableCell align="right" sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title="Editar" placement="top" arrow>
            <IconButton color={quickEdit.value ? 'inherit' : 'default'} onClick={quickEdit.onTrue}>
              <Iconify icon="solar:pen-bold" />
            </IconButton>
          </Tooltip>

          <IconButton color={popover.open ? 'inherit' : 'default'} onClick={popover.onOpen}>
            <Iconify icon="eva:more-vertical-fill" />
          </IconButton>
        </TableCell>
      </TableRow>

      {quickEdit.value && (
        <MoverQuickEditForm currentMover={row} open={quickEdit.value} onClose={quickEdit.onFalse} />
      )}
      <CustomPopover
        open={popover.open}
        onClose={popover.onClose}
        arrow="right-top"
        sx={{ width: 140 }}
      >
        <MenuItem
          onClick={() => {
            confirm.onTrue();
            popover.onClose();
          }}
          sx={{ color: 'error.main' }}
        >
          <Iconify icon="solar:trash-bin-trash-bold" />
          Remover
        </MenuItem>
      </CustomPopover>

      <ConfirmDialog
        open={confirm.value}
        onClose={confirm.onFalse}
        title="Remover movimentador"
        content={`Você realmente deseja remover o movimentador "${row.name}"?`}
        action={
          <Button variant="contained" color="error" onClick={onDeleteRow}>
            Remover
          </Button>
        }
      />
    </>
  );
}
