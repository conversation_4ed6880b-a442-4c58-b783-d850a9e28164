'use client';

import { useState, useEffect, useCallback } from 'react';

import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';

import { paths } from 'src/routes/paths';

import { useBoolean } from 'src/hooks/use-boolean';

import { useCompanyContext } from 'src/company/hooks';
import { useMoverDeleteMutation } from 'src/mutations/mover/mover-mutation';
import { useMoverSuspenseListQuery } from 'src/queries/warehouse/mover-query';

import Iconify from 'src/components/iconify';
import Scrollbar from 'src/components/scrollbar';
import { useSettingsContext } from 'src/components/settings';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs';
import {
  useTable,
  TableNoData,
  TableSkeleton,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

import { IMover } from 'src/types/mover';

import MoverTableRow from '../movers-table-row';
import MoverQuickNewForm from '../movers-quick-new-form';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: 'name', label: 'Name', width: 200 },
  { id: 'type', label: 'Tipo', width: 220 },
  { id: 'width', label: 'Largura', width: 100 },
  { id: 'length', label: 'Comprimento', width: 100 },
  { id: 'height', label: 'Altura', width: 100 },
  { id: 'weightCapacity', label: 'Capacidade de carga', width: 100 },
  { id: 'createdAt', label: 'Criado em', width: 100 },
  { id: 'updatedAt', label: 'Alterado em', width: 100 },
  { id: '', width: 88 },
];

// ----------------------------------------------------------------------

export default function MoverListView() {
  const { currentCompany } = useCompanyContext();

  const settings = useSettingsContext();

  const table = useTable();

  const newMover = useBoolean();

  const { mutateAsync } = useMoverDeleteMutation();

  const [tableData, setTableData] = useState<IMover[]>([]);

  const {
    data: { metadata, movers },
    isFetching,
    isRefetching,
    refetch,
  } = useMoverSuspenseListQuery(
    {
      companyId: currentCompany?.id!,
      warehouseId: currentCompany?.warehouseId!,
    },
    { page: table.page + 1, limit: table.rowsPerPage }
  );

  useEffect(() => {
    setTableData(movers || []);
  }, [movers]);

  useEffect(() => {
    refetch();
  }, [refetch, table.page, table.rowsPerPage]);

  const notFound = !isFetching && !isRefetching && !tableData.length;

  const handleDeleteRow = useCallback(
    (moverId: string) => {
      mutateAsync({ companyId: currentCompany?.id!, moverId });
    },
    [mutateAsync, currentCompany]
  );

  return (
    <>
      <Container maxWidth={settings.themeStretch ? false : 'lg'}>
        <CustomBreadcrumbs
          heading="Movimentadores"
          links={[
            { name: 'Dashboard', href: paths.dashboard.root },
            { name: 'Movimentadores', href: paths.dashboard.movers.root },
            { name: 'Lista' },
          ]}
          action={
            <Button
              color="success"
              variant="contained"
              onClick={newMover.onTrue}
              startIcon={<Iconify icon="mingcute:add-line" />}
            >
              Novo movimentador
            </Button>
          }
          sx={{
            mb: { xs: 3, md: 5 },
          }}
        />

        <Card>
          <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
            <Scrollbar>
              <Table stickyHeader size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
                <TableHeadCustom
                  order={table.order}
                  orderBy={table.orderBy}
                  headLabel={TABLE_HEAD}
                />

                <TableBody>
                  {isFetching || isRefetching
                    ? Array.from({ length: table.rowsPerPage }).map((_, index) => (
                        <TableSkeleton key={`table-row-skeleton-${index}`} />
                      ))
                    : tableData.map((row) => (
                        <MoverTableRow
                          key={row.id}
                          row={row}
                          onDeleteRow={() => handleDeleteRow(row.id)}
                        />
                      ))}

                  <TableNoData notFound={notFound} />
                </TableBody>
              </Table>
            </Scrollbar>
          </TableContainer>

          <TablePaginationCustom
            totalPages={Math.floor(tableData.length / table.rowsPerPage)}
            count={metadata.total}
            page={table.page}
            rowsPerPage={table.rowsPerPage}
            onPageChange={table.onChangePage}
            onRowsPerPageChange={table.onChangeRowsPerPage}
            //
            dense={table.dense}
            onChangeDense={table.onChangeDense}
          />
        </Card>
      </Container>

      <MoverQuickNewForm open={newMover.value} onClose={newMover.onFalse} />
    </>
  );
}
