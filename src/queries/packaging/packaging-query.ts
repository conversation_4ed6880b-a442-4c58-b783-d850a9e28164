import {
  useQuery,
  UseQueryResult,
  UseQueryOptions,
  useSuspenseQuery,
  UseSuspenseQueryResult,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';

import { PackagingRepository } from 'src/repositories/packaging';

import { IPackaging, PackagingList } from 'src/types/packaging';
import { IPagination, IPaginationParams } from 'src/types/pagination';

export const usePackagingListQuery = (
  { params }: { params: IPaginationParams },
  queryProps?: Omit<UseQueryOptions<IPagination<PackagingList>>, 'queryKey' | 'queryFn'>
): UseQueryResult<IPagination<PackagingList>> =>
  useQuery({
    queryKey: ['packagings', 'list', params],
    queryFn: () => PackagingRepository.getAll({ params }),
    ...queryProps,
  });

export const usePackagingListSuspenseQuery = (
  { params }: { params: IPaginationParams },
  queryProps?: Omit<UseSuspenseQueryOptions<IPagination<PackagingList>>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<IPagination<PackagingList>> =>
  useSuspenseQuery({
    queryKey: ['packagings', 'list', params],
    queryFn: () => PackagingRepository.getAll({ params }),
    ...queryProps,
  });

// show

export const usePackagingShowQuery = (
  { packagingId }: { packagingId: string },
  queryProps?: Omit<UseSuspenseQueryOptions<IPackaging>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<IPackaging> =>
  useSuspenseQuery({
    queryKey: ['packagings', 'details', packagingId],
    queryFn: () => PackagingRepository.show({ packagingId }),
    ...queryProps,
  });

export const usePackagingShowSuspenseQuery = (
  { packagingId }: { packagingId: string },
  queryProps?: Omit<UseSuspenseQueryOptions<IPackaging>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<IPackaging> =>
  useSuspenseQuery({
    queryKey: ['packagings', 'details', packagingId],
    queryFn: () => PackagingRepository.show({ packagingId }),
    ...queryProps,
  });
