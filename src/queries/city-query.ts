import {
  useQuery,
  UseQueryResult,
  UseQueryOptions,
  useSuspenseQuery,
  UseSuspenseQueryResult,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';

import CityRepository from 'src/repositories/city-repository';

import { ICity } from 'src/types/city';

export const useCityListQuery = (
  { stateUf }: { stateUf: string },
  queryProps?: Omit<UseQueryOptions<ICity[]>, 'queryKey' | 'queryFn'>
): UseQueryResult<ICity[]> =>
  useQuery({
    queryKey: ['states', 'list', stateUf, 'cities', 'list'],
    queryFn: () => CityRepository.getAll({ stateUf }),
    ...queryProps,
  });

export const useCityListSuspenseQuery = (
  { stateUf }: { stateUf: string },
  queryProps?: Omit<UseSuspenseQueryOptions<ICity[]>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<ICity[]> =>
  useSuspenseQuery({
    queryKey: ['states', 'list', stateUf, 'cities', 'list'],
    queryFn: () => CityRepository.getAll({ stateUf }),
    ...queryProps,
  });

// show --------------------------------------------------------------

export const useCityShowQuery = (
  { stateUf, cityId }: { stateUf: string; cityId: string },
  queryProps?: Omit<UseQueryOptions<ICity>, 'queryKey' | 'queryFn'>
): UseQueryResult<ICity> =>
  useQuery({
    queryKey: ['states', 'list', stateUf, 'cities', 'details', cityId],
    queryFn: () => CityRepository.getCity({ stateUf, cityId }),
    ...queryProps,
  });

export const useCityShowSuspenseQuery = (
  { stateUf, cityId }: { stateUf: string; cityId: string },
  queryProps?: Omit<UseSuspenseQueryOptions<ICity>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<ICity> =>
  useSuspenseQuery({
    queryKey: ['states', 'list', stateUf, 'cities', 'details', cityId],
    queryFn: () => CityRepository.getCity({ stateUf, cityId }),
    ...queryProps,
  });
