import {
  useQuery,
  UseQueryResult,
  UseQueryOptions,
  useSuspenseQuery,
  UseSuspenseQueryResult,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';

import TagRepository, { TagRepositoryTypes } from 'src/repositories/tag-repository';

import { ITag } from 'src/types/tag';

export const useTagListQuery = (
  queryProps?: Omit<UseQueryOptions<ITag[]>, 'queryKey' | 'queryFn'>
): UseQueryResult<ITag[]> =>
  useQuery({
    queryKey: ['tags', 'list'],
    queryFn: () => TagRepository.getAll(),
    ...queryProps,
  });

export const useTagListSuspenseQuery = (
  queryProps?: Omit<UseSuspenseQueryOptions<ITag[]>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<ITag[]> =>
  useSuspenseQuery({
    queryKey: ['tags', 'list'],
    queryFn: () => TagRepository.getAll(),
    ...queryProps,
  });

// show --------------------------------------------------------------

export const useTagShowQuery = (
  { tagId }: TagRepositoryTypes['show'],
  queryProps?: Omit<UseQueryOptions<ITag>, 'queryKey' | 'queryFn'>
): UseQueryResult<ITag> =>
  useQuery({
    queryKey: ['tags', 'details', tagId],
    queryFn: () => TagRepository.getState({ tagId }),
    ...queryProps,
  });

export const useTagShowSuspenseQuery = (
  { tagId }: TagRepositoryTypes['show'],
  queryProps?: Omit<UseSuspenseQueryOptions<ITag>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<ITag> =>
  useSuspenseQuery({
    queryKey: ['tags', 'details', tagId],
    queryFn: () => TagRepository.getState({ tagId }),
    ...queryProps,
  });
