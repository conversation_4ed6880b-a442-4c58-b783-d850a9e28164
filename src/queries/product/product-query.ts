import {
  useQuery,
  UseQueryResult,
  UseQueryOptions,
  useSuspenseQuery,
  UseSuspenseQueryResult,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';

import { ProductRepository } from 'src/repositories/product';

import { IProduct } from 'src/types/product';
import { IPagination, IPaginationParams, IProductPagination } from 'src/types/pagination';

export const useProductListQuery = (
  { params }: { params: IPaginationParams },
  queryProps?: Omit<UseQueryOptions<IPagination<IProductPagination>>, 'queryKey' | 'queryFn'>
): UseQueryResult<IPagination<IProductPagination>> =>
  useQuery({
    queryKey: ['products', 'list', params],
    queryFn: () => ProductRepository.getAll({ params }),
    ...queryProps,
  });

export const useProductListSuspenseQuery = (
  { params }: { params: IPaginationParams },
  queryProps?: Omit<
    UseSuspenseQueryOptions<IPagination<IProductPagination>>,
    'queryKey' | 'queryFn'
  >
): UseSuspenseQueryResult<IPagination<IProductPagination>> =>
  useSuspenseQuery({
    queryKey: ['products', 'list', params],
    queryFn: () => ProductRepository.getAll({ params }),
    ...queryProps,
  });

// show

export const useProductShowQuery = (
  { productId }: { productId: string },
  queryProps?: Omit<UseSuspenseQueryOptions<IProduct>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<IProduct> =>
  useSuspenseQuery({
    queryKey: ['products', 'details', productId],
    queryFn: () => ProductRepository.show({ productId }),
    ...queryProps,
  });

export const useProductShowSuspenseQuery = (
  { productId }: { productId: string },
  queryProps?: Omit<UseSuspenseQueryOptions<IProduct>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<IProduct> =>
  useSuspenseQuery({
    queryKey: ['products', 'details', productId],
    queryFn: () => ProductRepository.show({ productId }),
    ...queryProps,
  });
