import {
  useQuery,
  UseQueryResult,
  UseQueryOptions,
  useSuspenseQuery,
  UseSuspenseQueryResult,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';

import { CompanyRepository } from 'src/repositories/company';

import { ICompany } from 'src/types/company';

export const useCompanyListQuery = (
  queryProps?: Omit<UseQueryOptions<ICompany[]>, 'queryKey' | 'queryFn'>
): UseQueryResult<ICompany[]> =>
  useQuery({
    queryKey: ['companies', 'list'],
    queryFn: () => CompanyRepository.getAll(),
    ...queryProps,
  });

export const useCompanyListSuspenseQuery = (
  queryProps?: Omit<UseSuspenseQueryOptions<ICompany[]>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<ICompany[]> =>
  useSuspenseQuery({
    queryKey: ['companies', 'list'],
    queryFn: CompanyRepository.getAll,
    ...queryProps,
  });

// show

export const useCompanyShowQuery = (
  { companyId }: { companyId: string },
  queryProps?: Omit<UseSuspenseQueryOptions<ICompany>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<ICompany> =>
  useSuspenseQuery({
    queryKey: ['companies', 'details', companyId],
    queryFn: () => CompanyRepository.show({ companyId }),
    ...queryProps,
  });

export const useCompanyShowSuspenseQuery = (
  { companyId }: { companyId: string },
  queryProps?: Omit<UseSuspenseQueryOptions<ICompany>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<ICompany> =>
  useSuspenseQuery({
    queryKey: ['companies', 'details', companyId],
    queryFn: () => CompanyRepository.show({ companyId }),
    ...queryProps,
  });
