import {
  useQuery,
  UseQueryResult,
  UseQueryOptions,
  useSuspenseQuery,
  UseSuspenseQueryResult,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';

import StateRepository from 'src/repositories/state-repository';

import { IState } from 'src/types/state';

export const useStateListQuery = (
  queryProps?: Omit<UseQueryOptions<IState[]>, 'queryKey' | 'queryFn'>
): UseQueryResult<IState[]> =>
  useQuery({
    queryKey: ['states', 'list'],
    queryFn: StateRepository.getAll,
    ...queryProps,
  });

export const useStateListSuspenseQuery = (
  queryProps?: Omit<UseSuspenseQueryOptions<IState[]>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<IState[]> =>
  useSuspenseQuery({
    queryKey: ['states', 'list'],
    queryFn: StateRepository.getAll,
    ...queryProps,
  });

// show --------------------------------------------------------------

export const useStateShowQuery = (
  { stateUf }: { stateUf: string },
  queryProps?: Omit<UseQueryOptions<IState>, 'queryKey' | 'queryFn'>
): UseQueryResult<IState> =>
  useQuery({
    queryKey: ['states', 'details', stateUf],
    queryFn: () => StateRepository.getState({ stateUf }),
    ...queryProps,
  });

export const useStateShowSuspenseQuery = (
  { stateUf }: { stateUf: string },
  queryProps?: Omit<UseSuspenseQueryOptions<IState>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<IState> =>
  useSuspenseQuery({
    queryKey: ['states', 'details', stateUf],
    queryFn: () => StateRepository.getState({ stateUf }),
    ...queryProps,
  });
