import {
  useSuspenseQuery,
  UseSuspenseQueryResult,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';

import IntegrationRepository from 'src/repositories/tenant/integration-repository';

import { IIntegration } from 'src/types/integration';

export const useIntegrationListQuery = (
  queryProps?: Omit<UseSuspenseQueryOptions<IIntegration[]>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<IIntegration[]> =>
  useSuspenseQuery({
    queryKey: ['integrations', 'list'],
    queryFn: () => IntegrationRepository.getAll(),
    ...queryProps,
  });

export const useIntegrationShowQuery = (
  integrationId: string,
  queryProps?: Omit<UseSuspenseQueryOptions<IIntegration>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<IIntegration> =>
  useSuspenseQuery({
    queryKey: ['integrations', 'details', integrationId],
    queryFn: () => IntegrationRepository.show({ integrationId }),
    ...queryProps,
  });
