import {
  useSuspenseQuery,
  UseSuspenseQueryResult,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';

import { TenantRepository } from 'src/repositories/tenant';

import { ITenant } from 'src/types/tenant';
import { IWarehouse } from 'src/types/warehouse';

export const useTenantListQuery = (
  queryProps?: Omit<UseSuspenseQueryOptions<ITenant[]>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<ITenant[]> =>
  useSuspenseQuery({
    queryKey: ['tenants', 'list'],
    queryFn: TenantRepository.getAll,
    ...queryProps,
  });

export const useTenantListWarehouseQuery = (
  tenantId: string,
  queryProps?: Omit<
    UseSuspenseQueryOptions<{ companyName: string; warehouses: IWarehouse[] }[]>,
    'queryKey' | 'queryFn'
  >
): UseSuspenseQueryResult<{ companyName: string; warehouses: IWarehouse[] }[]> =>
  useSuspenseQuery({
    queryKey: ['tenants', 'list', 'warehouse'],
    queryFn: () => TenantRepository.getAllWarehouse({ tenantId }),
    ...queryProps,
  });

export const useTenantShowQuery = (
  tenantId: string,
  queryProps?: Omit<UseSuspenseQueryOptions<ITenant>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<ITenant> =>
  useSuspenseQuery({
    queryKey: ['tenants', 'details', tenantId],
    queryFn: () => TenantRepository.show({ tenantId }),
    ...queryProps,
  });
