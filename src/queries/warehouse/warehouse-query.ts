import {
  useQuery,
  UseQueryResult,
  UseQueryOptions,
  useSuspenseQuery,
  UseSuspenseQueryResult,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';

import { WarehouseRepository } from 'src/repositories/warehouse';

import { IWarehouse } from 'src/types/warehouse';

export const useWarehouseListQuery = (
  { companyId }: { companyId: string },
  queryProps?: Omit<UseQueryOptions<IWarehouse[]>, 'queryKey' | 'queryFn'>
): UseQueryResult<IWarehouse[]> =>
  useQuery({
    queryKey: ['companies', companyId, 'warehouses', 'list'],
    queryFn: () => WarehouseRepository.getAll({ companyId }),
    ...queryProps,
  });

export const useWarehouseListSuspenseQuery = (
  { companyId }: { companyId: string },
  queryProps?: Omit<UseSuspenseQueryOptions<IWarehouse[]>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<IWarehouse[]> =>
  useSuspenseQuery({
    queryKey: ['companies', companyId, 'warehouses', 'list'],
    queryFn: () => WarehouseRepository.getAll({ companyId }),
    ...queryProps,
  });

export const useWarehouseShowQuery = (
  { companyId, warehouseId }: { companyId: string; warehouseId: string },
  queryProps?: Omit<UseQueryOptions<IWarehouse>, 'queryKey' | 'queryFn'>
): UseQueryResult<IWarehouse> =>
  useQuery({
    queryKey: ['companies', companyId, 'warehouses', 'details'],
    queryFn: () => WarehouseRepository.show({ companyId, warehouseId }),
    ...queryProps,
  });

export const useWarehouseShowSuspenseQuery = (
  { companyId, warehouseId }: { companyId: string; warehouseId: string },
  queryProps?: Omit<UseSuspenseQueryOptions<IWarehouse>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<IWarehouse> =>
  useSuspenseQuery({
    queryKey: ['tenants', 'companies', companyId, 'warehouses', 'details'],
    queryFn: () => WarehouseRepository.show({ companyId, warehouseId }),
    ...queryProps,
  });
