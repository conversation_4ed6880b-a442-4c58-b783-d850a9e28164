import {
  useQuery,
  UseQueryResult,
  UseQueryOptions,
  useSuspenseQuery,
  UseSuspenseQueryResult,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';

import MoverRepository from 'src/repositories/warehouse/mover-repository';

import { IPagination } from 'src/types/pagination';
import { MoverList, IMoverQuery } from 'src/types/mover';

export const useMoverListQuery = (
  { companyId, warehouseId }: { companyId: string; warehouseId: string },
  params?: IMoverQuery,
  queryProps?: Omit<UseQueryOptions<IPagination<MoverList>>, 'queryKey' | 'queryFn'>
): UseQueryResult<IPagination<MoverList>> =>
  useQuery({
    queryKey: ['companies', companyId, 'movers', 'list'],
    queryFn: () => MoverRepository.getAll({ companyId, warehouseId, params }),
    ...queryProps,
  });

export const useMoverSuspenseListQuery = (
  { companyId, warehouseId }: { companyId: string; warehouseId: string },
  params?: IMoverQuery,
  queryProps?: Omit<UseSuspenseQueryOptions<IPagination<MoverList>>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<IPagination<MoverList>> =>
  useSuspenseQuery({
    queryKey: ['companies', companyId, 'movers', 'list'],
    queryFn: () => MoverRepository.getAll({ companyId, warehouseId, params }),
    ...queryProps,
  });
