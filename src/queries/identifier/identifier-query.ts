import {
  useQuery,
  UseQueryResult,
  UseQueryOptions,
  useSuspenseQuery,
  UseSuspenseQueryResult,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';

import IdentifierRepository from 'src/repositories/identifier/identifier';

import { IIdentifierScan } from 'src/types/identifier';

export const useIdentifierShowQuery = (
  { value }: { value: string },
  queryProps?: Omit<UseQueryOptions<IIdentifierScan>, 'queryKey' | 'queryFn'>
): UseQueryResult<IIdentifierScan> =>
  useQuery({
    queryKey: ['identifiers', 'details', value],
    queryFn: () => IdentifierRepository.show({ value }),
    ...queryProps,
  });

export const useIdentifierShowSuspenseQuery = (
  { value }: { value: string },
  queryProps?: Omit<UseSuspenseQueryOptions<IIdentifierScan>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<IIdentifierScan> =>
  useSuspenseQuery({
    queryKey: ['identifiers', 'details', value],
    queryFn: () => IdentifierRepository.show({ value }),
    ...queryProps,
  });
