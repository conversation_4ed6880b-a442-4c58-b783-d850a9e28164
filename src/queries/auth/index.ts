import {
  useQuery,
  UseQueryOptions,
  useSuspenseQuery,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';

// eslint-disable-next-line import/no-cycle
import AuthRepository from 'src/repositories/auth-repository';

import { IAuthMyAccount } from 'src/types/auth';

export function useMyAccountQuery(
  queryProps?: Omit<UseQueryOptions<IAuthMyAccount>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: ['me'],
    queryFn: AuthRepository.myAccount,
    ...queryProps,
  });
}

export function useMyAccountSuspenseQuery(
  queryProps?: Omit<UseSuspenseQueryOptions<IAuthMyAccount>, 'queryKey' | 'queryFn'>
) {
  return useSuspenseQuery({
    queryKey: ['me'],
    queryFn: AuthRepository.myAccount,
    ...queryProps,
  });
}
