import {
  useQuery,
  UseQueryResult,
  UseQueryOptions,
  useSuspenseQuery,
  UseSuspenseQueryResult,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';

import MagazordShopRepository from 'src/repositories/integrations/magazord/magazord-shop.repository';

import { IMagazordShopList, IMagazordShopQuery } from 'src/types/integrations/magazord/shop';

export const useMagazordShopListQuery = (
  params: IMagazordShopQuery,
  queryProps?: Omit<UseQueryOptions<IMagazordShopList>, 'queryKey' | 'queryFn'>
): UseQueryResult<IMagazordShopList> =>
  useQuery({
    queryKey: ['magazord', 'shops', 'list'],
    queryFn: () => MagazordShopRepository.getAll(params),
    ...queryProps,
  });

export const useMagazordShopSuspenseListQuery = (
  params: IMagazordShopQuery,
  queryProps?: Omit<UseSuspenseQueryOptions<IMagazordShopList>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<IMagazordShopList> =>
  useSuspenseQuery({
    queryKey: ['magazord', 'shops', 'list'],
    queryFn: () => MagazordShopRepository.getAll(params),
    ...queryProps,
  });

// export const useMagazordShopShowQuery = (
//   accountId: string,
//   queryProps?: Omit<UseQueryOptions, 'queryKey' | 'queryFn'>
// ): UseQueryResult => {
//   const { account } = useAuthContext();
//   const tenantId = account?.tenantId!;

//   return useQuery({
//     queryKey: ['magazord', 'shops', 'details', accountId],
//     queryFn: () => MagazordShopRepository.show({ tenantId, accountId }),
//     ...queryProps,
//   });
// };

// export const useMagazordShopSuspenseShowQuery = (
//   accountId: string,
//   queryProps?: Omit<UseSuspenseQueryOptions, 'queryKey' | 'queryFn'>
// ): UseSuspenseQueryResult => {
//   const { account } = useAuthContext();
//   const tenantId = account?.tenantId!;

//   return useSuspenseQuery({
//     queryKey: ['magazord', 'shops', 'details', accountId],
//     queryFn: () => MagazordShopRepository.show({ tenantId, accountId }),
//     ...queryProps,
//   });
// };
