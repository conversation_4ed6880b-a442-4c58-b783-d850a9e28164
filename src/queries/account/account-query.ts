import {
  useQuery,
  UseQueryResult,
  UseQueryOptions,
  useSuspenseQuery,
  UseSuspenseQueryResult,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';

import { AccountRepository } from 'src/repositories/account';

import { IPagination } from 'src/types/pagination';
import { AccountList, IAccountQuery } from 'src/types/account';

export const useAccountListQuery = (
  params?: IAccountQuery,
  queryProps?: Omit<UseQueryOptions<IPagination<AccountList>>, 'queryKey' | 'queryFn'>
): UseQueryResult<IPagination<AccountList>> =>
  useQuery({
    queryKey: ['accounts', 'list'],
    queryFn: () => AccountRepository.getAll({ params }),
    ...queryProps,
  });

export const useAccountSuspenseListQuery = (
  params?: IAccountQuery,
  queryProps?: Omit<UseSuspenseQueryOptions<IPagination<AccountList>>, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult<IPagination<AccountList>> =>
  useSuspenseQuery({
    queryKey: ['accounts', 'list'],
    queryFn: () => AccountRepository.getAll({ params }),
    ...queryProps,
  });

export const useAccountShowQuery = (
  accountId: string,
  queryProps?: Omit<UseQueryOptions, 'queryKey' | 'queryFn'>
): UseQueryResult =>
  useQuery({
    queryKey: ['accounts', 'details', accountId],
    queryFn: () => AccountRepository.show({ accountId }),
    ...queryProps,
  });

export const useAccountSuspenseShowQuery = (
  accountId: string,
  queryProps?: Omit<UseSuspenseQueryOptions, 'queryKey' | 'queryFn'>
): UseSuspenseQueryResult =>
  useSuspenseQuery({
    queryKey: ['accounts', 'details', accountId],
    queryFn: () => AccountRepository.show({ accountId }),
    ...queryProps,
  });
