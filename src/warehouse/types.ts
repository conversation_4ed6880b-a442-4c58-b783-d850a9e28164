// ----------------------------------------------------------------------

import { IWarehouse } from 'src/types/warehouse';

export type ActionMapType<M extends { [index: string]: any }> = {
  [Key in keyof M]: M[Key] extends undefined
    ? {
        type: Key;
      }
    : {
        type: Key;
        payload: M[Key];
      };
};

export type WarehouseType = null | IWarehouse;

export type WarehouseStateType = {
  loading: boolean;
  warehouses: IWarehouse[];
  status?: WarehouseStatusType;
  currentWarehouse: WarehouseType;
};

// ----------------------------------------------------------------------

export type WarehouseContextType = {
  loading?: boolean;
  warehouses: IWarehouse[];
  status: WarehouseStatusType;
  currentWarehouse: WarehouseType;
  changeWarehouse: (warehouseId: string) => void;
};

export enum WarehouseStatusType {
  LOADED = 'LOADED',
  LOADING = 'LOADING',
}
