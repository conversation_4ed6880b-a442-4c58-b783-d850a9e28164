/* eslint-disable import/no-cycle */

'use client';

import { isEqual } from 'lodash';
import { useRouter } from 'next/navigation';
import { useMemo, useEffect, useReducer, useCallback } from 'react';

import { useAuthContext } from 'src/auth/hooks';
import { useCompanyListQuery } from 'src/queries/company/company-query';

import { ICompany } from 'src/types/company';

import { CompanyContext } from './company-context';
import { getCompanyId, setCompanyId } from './utils';
import {
  CompanyType,
  ActionMapType,
  CompanyStateType,
  CompanyStatusType,
  CompanyContextType,
} from '../types';

// ----------------------------------------------------------------------

enum Types {
  INITIAL = 'INITIAL',
  LOADING = 'LOADING',
  AUTH_LOADED = 'AUTH_LOADED',
  CHANGE_COMPANY = 'CHANGE_COMPANY',
  REFRESH_COMPANIES = 'REFRESH_COMPANIES',
}

type Payload = {
  [Types.INITIAL]: {
    loading: boolean;
    companies: ICompany[];
    status: CompanyStatusType;
    currentCompany: CompanyType;
  };
  [Types.AUTH_LOADED]: {
    loading: boolean;
    status: CompanyStatusType;
  };
  [Types.CHANGE_COMPANY]: {
    currentCompany: CompanyType;
  };
  [Types.REFRESH_COMPANIES]: {
    companies: ICompany[];
    currentCompany: CompanyType;
  };
};

type ActionsType = ActionMapType<Payload>[keyof ActionMapType<Payload>];

// ----------------------------------------------------------------------

export const COMPANY_KEY = 'currentCompanyId';

const initialState: CompanyStateType = {
  loading: true,
  companies: [],
  currentCompany: null,
  status: CompanyStatusType.LOADING,
};

const reducer = (state: CompanyStateType, action: ActionsType) => {
  if (action.type === Types.INITIAL) {
    return {
      ...state,
      status: action.payload.status,
      loading: action.payload.loading,
      companies: action.payload.companies,
      currentCompany: action.payload.currentCompany,
    };
  }
  if (action.type === Types.AUTH_LOADED) {
    return {
      ...state,
      loading: false,
      status: CompanyStatusType.LOADED,
    };
  }
  if (action.type === Types.CHANGE_COMPANY) {
    return {
      ...state,
      currentCompany: action.payload.currentCompany,
    };
  }
  if (action.type === Types.REFRESH_COMPANIES) {
    // Skip update if companies and currentCompany are the same
    if (
      isEqual(state.companies, action.payload.companies) &&
      isEqual(state.currentCompany, action.payload.currentCompany)
    ) {
      return state;
    }
    return {
      ...state,
      companies: action.payload.companies,
      currentCompany: action.payload.currentCompany,
    };
  }
  return state;
};

// ----------------------------------------------------------------------

type Props = {
  children: React.ReactNode;
};

export function CompanyProvider({ children }: Props) {
  const [state, dispatch] = useReducer(reducer, initialState);

  const { refresh } = useRouter();

  const { account, loading: loadingAuth, authenticated } = useAuthContext();
  const tenantId = account?.tenantId!;

  const { data: companies = [] } = useCompanyListQuery({ enabled: !!tenantId && authenticated });

  useEffect(() => {
    if (!loadingAuth && authenticated) {
      dispatch({
        type: Types.AUTH_LOADED,
        payload: {
          loading: false,
          status: CompanyStatusType.LOADED,
        },
      });
    } else {
      dispatch({
        type: Types.INITIAL,
        payload: {
          loading: true,
          companies: [],
          currentCompany: null,
          status: CompanyStatusType.LOADING,
        },
      });
    }
  }, [loadingAuth, authenticated]);

  useEffect(() => {
    const currentCompanyId = getCompanyId();

    let currentCompany: CompanyType = null;

    if (companies.length === 1) {
      currentCompany = companies.at(0) ?? null;
    } else {
      currentCompany = companies.find((c) => c.id === currentCompanyId) ?? null;
    }

    if (currentCompany) {
      setCompanyId(currentCompany.id);
    }

    // Only dispatch if there's an actual change
    const companiesChanged = !isEqual(companies, state.companies);
    const companyChanged = !isEqual(currentCompany, state.currentCompany);

    if (companiesChanged || companyChanged) {
      dispatch({
        type: Types.REFRESH_COMPANIES,
        payload: {
          companies,
          currentCompany,
        },
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [companies]);

  const changeCompany = useCallback(
    (companyId: string) => {
      const currentCompany = state.companies.find((c) => c.id === companyId) ?? null;

      if (currentCompany) {
        setCompanyId(currentCompany.id);
      }

      dispatch({
        type: Types.CHANGE_COMPANY,
        payload: {
          currentCompany,
        },
      });

      refresh();
    },
    [refresh, state.companies]
  );

  // ----------------------------------------------------------------------

  const status = account ? CompanyStatusType.LOADED : CompanyStatusType.LOADING;

  const memoizedValue: CompanyContextType = useMemo(
    () => ({
      loading: state.loading,
      companies: state.companies,
      status: state.status ?? status,
      currentCompany: state.currentCompany,
      //
      changeCompany,
    }),
    [changeCompany, state, status]
  );

  return <CompanyContext.Provider value={memoizedValue}>{children}</CompanyContext.Provider>;
}
