/* eslint-disable import/no-cycle */

'use client';

import { isEqual } from 'lodash';
import { useRouter } from 'next/navigation';
import { useMemo, useEffect, useReducer, useCallback } from 'react';

import { useAuthContext } from 'src/auth/hooks';
import { getCompanyId } from 'src/company/context/utils';
import { useWarehouseListQuery } from 'src/queries/warehouse/warehouse-query';

import { IWarehouse } from 'src/types/warehouse';

import { WarehouseContext } from './warehouse-context';
import { getWarehouseId, setWarehouseId } from './utils';
import {
  ActionMapType,
  WarehouseType,
  WarehouseStateType,
  WarehouseStatusType,
  WarehouseContextType,
} from '../types';

// ----------------------------------------------------------------------

enum Types {
  INITIAL = 'INITIAL',
  LOADING = 'LOADING',
  AUTH_LOADED = 'AUTH_LOADED',
  CHANGE_WAREHOUSE = 'CHANGE_WAREHOUSE',
  REFRESH_WAREHOUSES = 'REFRESH_WAREHOUSES',
}

type Payload = {
  [Types.INITIAL]: {
    loading: boolean;
    warehouses: IWarehouse[];
    status: WarehouseStatusType;
    currentWarehouse: WarehouseType;
  };
  [Types.AUTH_LOADED]: {
    loading: boolean;
    status: WarehouseStatusType;
  };
  [Types.CHANGE_WAREHOUSE]: {
    currentWarehouse: WarehouseType;
  };
  [Types.REFRESH_WAREHOUSES]: {
    warehouses: IWarehouse[];
    currentWarehouse: WarehouseType;
  };
};

type ActionsType = ActionMapType<Payload>[keyof ActionMapType<Payload>];

// ----------------------------------------------------------------------

export const WAREHOUSE_KEY = 'currentWarehouseId';

const initialState: WarehouseStateType = {
  loading: true,
  warehouses: [],
  currentWarehouse: null,
  status: WarehouseStatusType.LOADING,
};

const reducer = (state: WarehouseStateType, action: ActionsType) => {
  if (action.type === Types.INITIAL) {
    return {
      ...state,
      status: action.payload.status,
      loading: action.payload.loading,
      warehouses: action.payload.warehouses,
      currentWarehouse: action.payload.currentWarehouse,
    };
  }
  if (action.type === Types.AUTH_LOADED) {
    return {
      ...state,
      loading: false,
      status: WarehouseStatusType.LOADED,
    };
  }
  if (action.type === Types.CHANGE_WAREHOUSE) {
    return {
      ...state,
      currentWarehouse: action.payload.currentWarehouse,
    };
  }
  if (action.type === Types.REFRESH_WAREHOUSES) {
    // Skip update if warehouses and currentWarehouse are the same
    if (
      isEqual(state.warehouses, action.payload.warehouses) &&
      isEqual(state.currentWarehouse, action.payload.currentWarehouse)
    ) {
      return state;
    }
    return {
      ...state,
      warehouses: action.payload.warehouses,
      currentWarehouse: action.payload.currentWarehouse,
    };
  }
  return state;
};

// ----------------------------------------------------------------------

type Props = {
  children: React.ReactNode;
};

export function WarehouseProvider({ children }: Props) {
  const [state, dispatch] = useReducer(reducer, initialState);

  const { refresh } = useRouter();

  const { account, loading: loadingAuth, authenticated } = useAuthContext();

  const companyId = getCompanyId();

  const { data: warehouses = [] } = useWarehouseListQuery(
    {
      companyId: companyId!,
    },
    {
      enabled: !!companyId && authenticated && !loadingAuth,
    }
  );

  useEffect(() => {
    if (!loadingAuth && authenticated) {
      dispatch({
        type: Types.AUTH_LOADED,
        payload: {
          loading: false,
          status: WarehouseStatusType.LOADED,
        },
      });
    } else {
      dispatch({
        type: Types.INITIAL,
        payload: {
          loading: true,
          warehouses: [],
          currentWarehouse: null,
          status: WarehouseStatusType.LOADING,
        },
      });
    }
  }, [loadingAuth, authenticated]);

  useEffect(() => {
    const currentWarehouseId = getWarehouseId();

    let currentWarehouse: WarehouseType = null;

    if (warehouses.length === 1) {
      currentWarehouse = warehouses.at(0) ?? null;
    } else {
      currentWarehouse = warehouses.find((c) => c.id === currentWarehouseId) ?? null;
    }

    if (currentWarehouse) {
      setWarehouseId(currentWarehouse.id);
    }

    // Only dispatch if there's an actual change
    const warehousesChanged = !isEqual(warehouses, state.warehouses);
    const warehouseChanged = !isEqual(currentWarehouse, state.currentWarehouse);

    if (warehousesChanged || warehouseChanged) {
      dispatch({
        type: Types.REFRESH_WAREHOUSES,
        payload: {
          warehouses,
          currentWarehouse,
        },
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [warehouses]);

  const changeWarehouse = useCallback(
    (warehouseId: string) => {
      const currentWarehouse = state.warehouses.find((c) => c.id === warehouseId) ?? null;

      if (currentWarehouse) {
        setWarehouseId(currentWarehouse.id);
      }

      dispatch({
        type: Types.CHANGE_WAREHOUSE,
        payload: {
          currentWarehouse,
        },
      });

      refresh();
    },
    [refresh, state.warehouses]
  );

  // ----------------------------------------------------------------------

  const status = account ? WarehouseStatusType.LOADED : WarehouseStatusType.LOADING;

  const memoizedValue: WarehouseContextType = useMemo(
    () => ({
      loading: state.loading,
      warehouses: state.warehouses,
      status: state.status ?? status,
      currentWarehouse: state.currentWarehouse,
      //
      changeWarehouse,
    }),
    [changeWarehouse, state, status]
  );

  return <WarehouseContext.Provider value={memoizedValue}>{children}</WarehouseContext.Provider>;
}
