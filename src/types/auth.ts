import { IAccount } from './account';

export type IAuthTokens = {
  accessToken: string;
  refreshToken: string;
};

// login --------------------------------

export type IAuthLogin = {
  account: Pick<IAccount, 'id' | 'name' | 'email' | 'role' | 'isSuper' | 'tenantId' | 'tenant'>;
  tokens: IAuthTokens;
};

export type IAuthLoginParams = {
  email: string;
  password: string;
};

// myaccount -----------------------------

export type IAuthMyAccount = {
  account: Pick<IAccount, 'id' | 'name' | 'email' | 'role' | 'isSuper' | 'tenantId' | 'tenant'>;
};

// logout --------------------------------

export type IAuthLogout = {
  refreshToken: string;
};

// refresh -------------------------------

export type IAuthRefresh = {
  tokens: IAuthTokens;
};

export type IAuthRefreshParams = {
  refreshToken: string;
};

// forgot password -------------------------------

export type IAuthForgotPasswordParams = {
  email: string;
};

// reset password -------------------------------

export type IAuthResetPasswordParams = {
  password: string;
  params: {
    token: string;
  };
};
