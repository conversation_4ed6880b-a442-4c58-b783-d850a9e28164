import { IdentifierValueType } from 'src/enum/identifier';
import { OrderOriginType } from 'src/enum/order-origin-type';

import { IRecordInformation } from './record-information';

// DTO

export type IProduct = {
  id: string;
  name: string;
  active: boolean;

  // TODO: MAYBE A ENUM
  measurementUnit: 'ml' | null;

  weight: number;
  length: number;

  width: number;
  height: number;

  tenantId: string;

  // RELATIONS
  categories: []; // TODO: CATEGORIES LIST TYPE
} & IRecordInformation;

// form types

export type IProductNewForm = {};

export type IProductUpdateForm = {};

type ProductWithCategories = {
  categories: {
    id: string;
    name: string;
    description: string | null;
    createdAt: Date;
    updatedAt: Date;
  }[];
};

type ProductWithIdentifiers = {
  identifiers: {
    id: string;
    value: string;
    valueType: IdentifierValueType;
    label: string | null;
    createdAt: Date;
    updatedAt: Date;
  }[];
};

export type IProductDto = {
  id: string;
  name: string;
  code: string;
  origin: OrderOriginType;
  height: number | null;
  length: number | null;
  weight: number | null;
  width: number | null;
  active: boolean;
  measurementUnit: string | null;
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
} & ProductWithCategories;

export type IProductDtoWithAllRelations = IProductDto &
  ProductWithIdentifiers &
  ProductWithCategories;
