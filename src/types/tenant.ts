import { IRecordInformation } from './record-information';

export type ITenant = {
  id: string;
  name: string;
  email: string;
  phone: string;
  responsibleName: string;
} & IRecordInformation;

export type ITenantNewData = {
  name: string;
  email: string;
  phone: string;

  responsibleName: string;
  password: string;
  confirmPassword: string;
};

export type ITenantUpdateData = {
  name?: string;
  email?: string;
  phone?: string;
};

// filters ----------------------------------------

export type ITenantTableFilterValue = string;

export type ITenantTableFilters = {
  name: string;
};

// form -------------------------------------------

export type ITenantNewForm = {
  name: string;
  email: string;
  phone: string;

  responsibleName: string;
  password: string;
};

export type ITenantUpdateForm = {
  name?: string;
  email?: string;
  phone?: string;
};
