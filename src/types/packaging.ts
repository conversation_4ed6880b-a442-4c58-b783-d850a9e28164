import { IdentifierValueType } from 'src/enum/identifier';

export type IPackaging = {
  id: string;
  name: string;
  width: number;
  height: number;
  length: number;
};

export type PackagingList = {
  packagings: IPackaging[];
};

export type IPackagingNewForm = {
  width: number;
  height: number;
  length: number;
};

export type IPackagingUpdateForm = {
  name?: string;
  width?: number;
  height?: number;
  length?: number;
};

// filters ----------------------------------------

export type IPackagingTableFilterValue = string;

export type IPackagingTableFilters = {
  name: string;
};

// DTO ----------------------------------------

export type IPackagingDto = {
  id: string;
  name: string;
  height: number;
  length: number;
  width: number;
  tenantId: string;
  identifiers: {
    id: string;
    value: string;
    valueType: IdentifierValueType;
    label: string | null;
    createdAt: Date;
    updatedAt: Date;
  }[];
  createdAt: Date;
  updatedAt: Date;
};
