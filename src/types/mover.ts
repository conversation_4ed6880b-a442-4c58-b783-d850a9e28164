import { MoverType } from 'src/enum/mover';
import { IdentifierValueType } from 'src/enum/identifier';
import { StockPositionType } from 'src/enum/stock-position';

import { IQuery } from './utils';
import { IRecordInformation } from './record-information';

export type IMover = {
  id: string;
  type: MoverType;
  name: string;
  height: number;
  length: number;
  width: number;
  weightCapacity: number;

  // FK
  warehouseId: string;
} & IRecordInformation;

export type MoverList = {
  movers: IMover[];
};

// form ----------------------------------------------------------------

export type IMoverNewForm = {
  height: number;
  length: number;
  type: MoverType;
  weightCapacity: number;
  width: number;
};

export type IWarehouseEditForm = {
  name?: string;
  width?: number;
  length?: number;
};

// data ----------------------------------------------------------------

export type IMoverData = {
  height: number;
  length: number;
  type: MoverType;
  weightCapacity: number;
  width: number;
};

// queries -----------------------------------------------

export type IMoverQuery = {} & IQuery;

// filters ----------------------------------------

export type IWarehouseTableFilterValue = string;

export type IWarehouseTableFilters = {
  name: string;
};

type Mover = {
  id: string;
  type: MoverType;
  name: string;
  height: number;
  length: number;
  width: number;
  weightCapacity: number;
  warehouseId: string;
  stockPositionId: string | null;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
};

type MoverWithStockPosition = {
  stockPosition: {
    id: string;
    floor: number;
    division: number;
    type: StockPositionType;
    warehouseId: string;
  } | null;
};

type MoverWithIdentifiers = {
  identifiers: {
    id: string;
    value: string;
    valueType: IdentifierValueType;
    label: string | null;
    createdAt: Date;
    updatedAt: Date;
  }[];
};

export type IMoverDto = MoverWithIdentifiers & Mover & MoverWithStockPosition;
