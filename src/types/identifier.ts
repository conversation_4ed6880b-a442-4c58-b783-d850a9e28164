import { IdentifierType, IdentifierValueType } from 'src/enum/identifier';

import { IMoverDto } from './mover';
import { IPackagingDto } from './packaging';
import { IStockPositionDto } from './stock-position';
import { IProductDtoWithAllRelations } from './product';
import { IRecordInformation } from './record-information';

export type IIdentifierList = {
  id: string;
  label: string;
  value: string;
  tenantId: string;
  identificableId: string;
  valueType: IdentifierValueType;
  identificableType: IdentifierType;
} & IRecordInformation;

export type IIdentifierDetails = {
  id: string;
  label: string;
  value: string;
  tenantId: string;
  identificableId: string;
  valueType: IdentifierValueType;
  identificableType: IdentifierType;
} & IRecordInformation;

export type IIdentifierScan =
  | {
      type: IdentifierType.PACKAGING;
      item: IPackagingDto;
    }
  | {
      type: IdentifierType.POSITION;
      item: IStockPositionDto;
    }
  | {
      type: IdentifierType.PRODUCT;
      item: IProductDtoWithAllRelations;
    }
  | {
      type: IdentifierType.MOVER;
      item: IMoverDto;
    };
