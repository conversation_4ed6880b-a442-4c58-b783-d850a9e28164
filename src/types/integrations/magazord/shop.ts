import { MagazordShopSettingsType } from 'src/enum/magazord-shop-settings-type';

import { ITimestamp } from 'src/types/timestamp';

export interface IMagazordShop extends ITimestamp {
  id: string;
  rNome: string;
  active: boolean;
  status: number;
  settings: {
    type: MagazordShopSettingsType;
    payload: {
      warehouseId: string;
    };
  };
  rCodigo: string;
  rUrl: string;
  rAtivo: string;
  rLojaId: string;
  rUrlImage: string;
  rAplicativo: string;
  integrationId: string;
}

// ---------------------------------------------------

export type IMagazordShopList = IMagazordShop[];

// ---------------------------------------------------

export type IMagazordShopQuery = {
  tenantId: string;
  integrationId: string;
}; // & IQuery;

// ---------------------------------------------------

export type IMagazordShopEditForm = Pick<IMagazordShop, 'settings' | 'status'>;
