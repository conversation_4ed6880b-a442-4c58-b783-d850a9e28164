import { CompanyTypes } from 'src/enum/company';

import { IRecordInformation } from './record-information';

export type ICompany = {
  id: string;
  name: string;
  cnpj: string | null;
  companyType: CompanyTypes | null;
} & IRecordInformation;

export type ICompanyData = {
  name: string;
  cnpj?: string;
  companyType: CompanyTypes;
};

// form -------------------------------------------

export type ICompanyNewForm = {
  name: string;
  cnpj?: string;
  companyType: CompanyTypes;
};

export type ICompanyEditForm = Partial<ICompanyNewForm>;

// filters ----------------------------------------

export type ICompanyTableFilterValue = string;

export type ICompanyTableFilters = {
  name: string;
};
