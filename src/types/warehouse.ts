import { IQuadrant } from './quadrant';
import { IRecordInformation } from './record-information';

export type IWarehouse = {
  id: string;
  name: string;
  width: number;
  length: number;

  // FK
  companyId: string;

  // RELATIONS
  quadrants?: IQuadrant[];
} & IRecordInformation;

// form ----------------------------------------------------------------

export type IWarehouseNewForm = {
  name: string;
  width?: number;
  length?: number;
};

export type IWarehouseEditForm = {
  name?: string;
  width?: number;
  length?: number;
};

// data ----------------------------------------------------------------

export type IWarehouseData = {
  name: string;
  width?: number;
  length?: number;
};

// filters ----------------------------------------

export type IWarehouseTableFilterValue = string;

export type IWarehouseTableFilters = {
  name: string;
};
