export interface IIntegration {
  id: string;
  tenantId: string;
  name: string;
  type: IntegrationType;
  slug: string;
  content: {
    token: string;
    url: string | null;
    type: string;
  };
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum IntegrationType {
  MAGAZORD = 'magazord',
}

export interface IIntegrationNewForm {
  name: string;
  type: IntegrationType;
  content: any;
  slug: string;
  active: boolean;
}

export interface IIntegrationUpdateForm {
  name?: string;
  type?: IntegrationType;
  url?: string;
  token?: string;
  password?: string;
  active?: boolean;
}

// filters ----------------------------------------

export type IIntegrationTableFilterValue = string;

export type IIntegrationTableFilters = {
  name: string;
};
