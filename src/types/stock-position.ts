import { StockPositionType } from 'src/enum/stock-position';
import { StockPositionContentType } from 'src/enum/stock-position-content-type';

export type IStockPositionDto = {
  id: string;
  division: number;
  floor: number;
  type: StockPositionType;
  contentType: StockPositionContentType;
  pickingQuadrantsIds: string[];
  quadrantsIds: string[];
  warehouseId: string;
  products: {
    id: string;
    name: string;
    // quantity of product stock position
    quantity: number;
    createdAt: Date;
    updatedAt: Date;
  }[];
  movers: {
    id: string;
    name: string;
    type: string;
    height: number;
    length: number;
    width: number;
    weightCapacity: number;
    products: {
      id: string;
      name: string;
      // quantity of product stock position
      quantity: number;
      createdAt: Date;
      updatedAt: Date;
    }[];
    companyId: string;
    createdAt: Date;
    updatedAt: Date;
  }[];
  createdAt: Date;
  updatedAt: Date;
};
