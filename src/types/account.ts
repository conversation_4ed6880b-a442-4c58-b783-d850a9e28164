import { IQuery } from './utils';
import { ITenant } from './tenant';
import { IRecordInformation } from './record-information';

export type IAccount = {
  id: string;
  name: string;
  email: string;
  isSuper: boolean;

  role: {
    getRoles: boolean;
    getAccounts: boolean;
    getCompanies: boolean;
    getWarehouses: boolean;
    getTenants: boolean;
    getProducts: boolean;
    manageProducts: boolean;
    manageTenants: boolean;
    manageRoles: boolean;
    manageAccounts: boolean;
    manageCompanies: boolean;
    manageWarehouses: boolean;
    manageIdentifiers: boolean;
    manageStock: boolean;
    manageIntegrations: boolean;
    getIntegrations: boolean;
    getTags: boolean;
    manageTags: boolean;
  };

  environmentId: string;
  environment: string;

  tenantId: string;
  tenant?: ITenant;
} & IRecordInformation;

export type AccountList = {
  accounts: IAccount[];
};

// form  --------------------------------------------------

export type IAccountEditForm = Pick<
  IAccount,
  'name' | 'email' | 'environment' | 'environmentId' | 'role'
>;

export type IAccountNewForm = Pick<
  IAccount,
  'name' | 'email' | 'environment' | 'environmentId' | 'role'
> & {
  password: string;
};

// backend  -----------------------------------------------

export type IAccountData = {
  name: string;
  email: string;
  password: string;
  role: {
    getRoles: boolean;
    getAccounts: boolean;
    getCompanies: boolean;
    getWarehouses: boolean;
    getTenants: boolean;
    getProducts: boolean;
    manageProducts: boolean;
    manageTenants: boolean;
    manageRoles: boolean;
    manageAccounts: boolean;
    manageCompanies: boolean;
    manageWarehouses: boolean;
    manageIdentifiers: boolean;
    manageStock: boolean;
    manageIntegrations: boolean;
    getIntegrations: boolean;
    getTags: boolean;
    manageTags: boolean;
  };
  environment: string;
  environmentId: string;
};

// queries -----------------------------------------------

export type IAccountQuery = {} & IQuery;

// filters -----------------------------------------------

export type IAccountTableFilters = {};

export type IAccountTableFilterValue = string;
