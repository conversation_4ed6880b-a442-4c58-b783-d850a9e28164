import { object, string, boolean } from 'yup';

import { IntegrationType } from 'src/types/integration';

export const NewIntegrationSchema = object().shape({
  name: string().required('Nome é obrigatório'),
  type: string().oneOf(Object.values(IntegrationType)).required('Tipo é obrigatório'),
  active: boolean().required(),
  slug: string().required('Slug é obrigatório'),
  content: object().when(['type'], (values, schema) => {
    const [type] = values;

    // eslint-disable-next-line default-case
    switch (type) {
      case IntegrationType.MAGAZORD:
        return schema
          .shape({
            token: string().required('Token é obrigatório'),
            password: string().required('Password é obrigatório'),
            url: string().required('URL é obrigatório'),
          })
          .required();
      // case IntegrationType.GOALFY:
      //   return schema
      //     .shape({
      //       token: string().required('Token é obrigatório'),
      //       url: string().required('URL é obrigatório'),
      //     })
      //     .required();

      // case IntegrationType.MASTER:
      //   return schema.shape({
      //     token: string().required('Token é obrigatório'),
      //     url: string().required('URL é obrigatório'),
      //   });
    }

    return schema.required();
  }),
});

export const EditIntegrationSchema = (type: IntegrationType) =>
  object({
    name: string().required('Nome é obrigatório'),
    active: boolean().required(),
    slug: string().required('Slug é obrigatório'),
    content: object().when(['type'], (values, schema) => {
      // eslint-disable-next-line default-case
      switch (type) {
        case IntegrationType.MAGAZORD:
          return schema
            .shape({
              token: string().required('Token é obrigatório'),
              password: string().required('Password é obrigatório'),
              url: string().required('URL é obrigatório'),
            })
            .required();
        //   case IntegrationType.GOALFY:
        //     return schema
        //       .shape({
        //         token: string().required('Token é obrigatório'),
        //         url: string().required('URL é obrigatório'),
        //       })
        //       .required();
      }

      return schema.required();
    }),
  });
