import { object, string } from 'yup';

import {
  PasswordSchema,
  NameRequiredSchema,
  EmailOptionalSchema,
  EmailRequiredSchema,
  PhoneRequiredSchema,
  ConfirmPasswordSchema,
} from 'src/schemas/utils';

export const NewTenantSchema = object().shape({
  name: NameRequiredSchema,
  email: EmailRequiredSchema,
  phone: PhoneRequiredSchema,

  // First company data (matriz)
  password: PasswordSchema,
  confirmPassword: ConfirmPasswordSchema('password'),
  responsibleName: NameRequiredSchema,
});

export const UpdateTenantSchema = object().shape({
  name: string().optional(),
  email: EmailOptionalSchema,
  phone: string().optional(),
});
