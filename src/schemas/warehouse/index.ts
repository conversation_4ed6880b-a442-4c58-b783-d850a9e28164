import { number, object, string, boolean } from 'yup';

import { NameRequiredSchema } from '../utils';

export const NewWarehouseSchema = object().shape({
  name: NameRequiredSchema,
  layout: boolean().optional(),
  length: number().integer().positive().required('Largura é obrigatória'),
  width: number().integer().positive().required('Comprimento é obrigatório'),
});

export const UpdateWarehouseSchema = object().shape({
  name: string().optional(),
  length: number().integer().positive().required('Largura é obrigatória').optional(),
  width: number().integer().positive().required('Comprimento é obrigatório').optional(),
});
