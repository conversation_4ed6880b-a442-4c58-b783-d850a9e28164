import { object, string, boolean } from 'yup';

export const UpdateAccountSchema = object().shape({
  name: string().required('Nome é obrigatório'),
  role: object().shape({
    getRoles: boolean().required(),
    getAccounts: boolean().required(),
    getCompanies: boolean().required(),
    getWarehouses: boolean().required(),
    getTenants: boolean().required(),
    getProducts: boolean().required(),
    manageProducts: boolean().required(),
    manageTenants: boolean().required(),
    manageRoles: boolean().required(),
    manageAccounts: boolean().required(),
    manageCompanies: boolean().required(),
    manageWarehouses: boolean().required(),
    manageIdentifiers: boolean().required(),
    manageStock: boolean().required(),
    manageIntegrations: boolean().required(),
    getIntegrations: boolean().required(),
    getTags: boolean().required(),
    manageTags: boolean().required(),
  }),
  email: string().required('Email é obrigatório').email('Email deve ser um email válido'),
  environment: string().required('Ambiente é obrigatório'),
  environmentId: string().required('Ambiente é obrigatório'),
});

export const CreateAccountSchema = object().shape({
  name: string().required('Nome é obrigatório'),
  role: object().shape({
    getRoles: boolean().required(),
    getAccounts: boolean().required(),
    getCompanies: boolean().required(),
    getWarehouses: boolean().required(),
    getTenants: boolean().required(),
    getProducts: boolean().required(),
    manageProducts: boolean().required(),
    manageTenants: boolean().required(),
    manageRoles: boolean().required(),
    manageAccounts: boolean().required(),
    manageCompanies: boolean().required(),
    manageWarehouses: boolean().required(),
    manageIdentifiers: boolean().required(),
    manageStock: boolean().required(),
    manageIntegrations: boolean().required(),
    getIntegrations: boolean().required(),
    getTags: boolean().required(),
    manageTags: boolean().required(),
  }),
  email: string().required('Email é obrigatório').email('Email deve ser um email válido'),
  password: string()
    .required('Senha é obrigatória')
    .min(8, 'A senha deve ter no mínimo 8 caracteres')
    .matches(/\d/, 'A senha deve conter ao menos 1 número')
    .matches(/[a-zA-Z]/, 'A senha deve conter ao menos uma letra'),
  environment: string().required('Ambiente é obrigatório'),
  environmentId: string().required('Ambiente é obrigatório'),
});
