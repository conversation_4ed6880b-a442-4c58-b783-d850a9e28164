import { object, string, number } from 'yup';

export const PartialMoveProductFormSchema = object().shape({
  fromPosition: string().required('Campo obrigatório'),
  toPosition: string().required('Campo obrigatório'),
  productId: string().required('Campo obrigatório'),
  quantity: number()
    .required('Campo obrigatório')
    .positive('Valor deve ser maior que 0')
    .integer('Valor deve ser um número inteiro'),
});

export const TotalMoveProductFormSchema = object().shape({
  fromPosition: string().required('Campo obrigatório'),
  toPosition: string().required('Campo obrigatório'),
  productId: string().required('Campo obrigatório'),
});

export const AllMoveProductFormSchema = object().shape({
  fromPosition: string().required('Campo obrigatório'),
  toPosition: string().required('Campo obrigatório'),
});
