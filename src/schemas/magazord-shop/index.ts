import { object, string, number } from 'yup';

import { MagazordShopSettingsType } from 'src/enum/magazord-shop-settings-type';

export const UpdateMagazordShopSchema = object().shape({
  settings: object().shape({
    type: string()
      .oneOf(Object.values(MagazordShopSettingsType))
      .required('Tipo de configuração é obrigatório'),
    payload: object().shape({
      warehouseId: string().required('ID do armazém é obrigatório'),
    }),
  }),
  status: number().required('Status é obrigatório'),
});
