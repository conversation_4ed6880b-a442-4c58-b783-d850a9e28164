import { number, object, string } from 'yup';

import { MoverType } from 'src/enum/mover';

export const MoverSchema = object().shape({
  height: number().min(0, 'Altura não pode ser negativa').required('rAltura é obrigatória'),
  length: number()
    .min(0, 'Comprimento não pode ser negativa')
    .required('rComprimento é obrigatório'),
  type: string().oneOf(Object.values(MoverType)).required('tipo é obrigatório'),
  weightCapacity: number()
    .min(0, 'Capacidade de carga não pode ser negativa')
    .required('capacidade de carga é obrigatória'),
  width: number().min(0, 'Largura não pode ser negativa').required('Largura é obrigatória'),
});
