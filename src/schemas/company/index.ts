import { object, string } from 'yup';

import { COMPANY_TYPES } from 'src/consts/company';

import { NameRequiredSchema } from '../utils';

export const NewCompanySchema = object().shape({
  name: NameRequiredSchema,
  cnpj: string().optional(),
  companyType: string().oneOf(COMPANY_TYPES).required(),
});

export const EditCompanySchema = object().shape({
  name: NameRequiredSchema,
  cnpj: string().optional(),
  companyType: string().oneOf(COMPANY_TYPES).optional(),
});
