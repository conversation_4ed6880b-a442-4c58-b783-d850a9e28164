/* eslint-disable no-useless-escape */
import { ref, mixed, string, number } from 'yup';

// password validation
export const PasswordSchema = string()
  .required('Senha é obrigatória')
  .matches(/^(?=.*[0-9])/, 'Senha deve conter ao menos um número')
  .matches(/^(?=.*[a-z])/, 'Senha deve conter ao menos uma letra minúscula')
  .matches(/^(?=.*[A-Z])/, 'Senha deve conter ao menos uma letra maiúscula')
  .min(8, 'Senha deve ter ao menos 8 caracteres');

export function ConfirmPasswordSchema(passwordFieldName: string) {
  return string()
    .required('Confirmação de senha é obrigatória')
    .oneOf([ref(passwordFieldName), ''], 'Senhas não conferem');
}

// name validation
export const NameRequiredSchema = string()
  .required('Nome é obrigatório')
  .min(3, 'Nome deve ter ao menos 3 caracteres');

// email validation
export const EmailRequiredSchema = string()
  .required('Email é obrigatório')
  .email('Email deve ser um endereço de email válido');

export const EmailOptionalSchema = string().optional();

// cnpj validation
export const CNPJRequiredSchema = string()
  .required('CNPJ é obrigatório')
  .matches(/^\d{2}\.\d{3}\.\d{3}\/\d{4}\.\d{2}$/, 'CNPJ inválido');

export const CNPJOptionalSchema = string().optional().nullable();

// cpf validation
export const CPFRequiredSchema = string()
  .required('CPF é obrigatório')
  .matches(/^\d{3}\.\d{3}\.\d{3}\-\d{2}$/, 'CPF inválido');

export const CPFOptionalSchema = string().optional().nullable();

// cep
export const CEPRequiredSchema = string()
  .required('CEP é obrigatório')
  .matches(/^\d{2}\.\d{3}\-\d{3}$/, 'CEP inválido');

export const CEPOptionalSchema = string().optional().nullable();

// phone validation
export const PhoneRequiredSchema = string()
  .required('Telefone é obrigatório')
  .matches(/^\(?\d{2}\)?[\s-]?[\s9]?\d{4}-?\d{4}$/, 'Telefone inválido');

export const PhoneOptionalSchema = string().optional();

export const VerifyCodeSchema = string().matches(/^\d{6}$/, 'Código inválido');

// stateId required
export const StateIdRequiredSchema = number()
  .required('Estado é obrigatório')
  .test('stateId', 'É necessário escolher um estado', (value) => value !== 0);

export const StateIdOptionalSchema = number().optional();

// stateUf required
export const StateUfRequiredSchema = mixed<string | object>()
  .required('Estado é obrigatório')
  .test('state_uf', 'É necessário escolher um estado', (value) => value !== '' && value !== 'none');

export const StateUfOptionalSchema = string().optional();

// cityId required
export const CityIdRequiredSchema = mixed<number | object>()
  .required('Cidade é obrigatória')
  .test(
    'not have state',
    'É necessário escolher o estado para escolher a cidade',
    (value, ctx) => ctx.parent.stateId && ctx.parent.stateId === 0
  )
  .test('cityId', 'É necessário escolher uma cidade', (value) => value !== 0);

export const CityIdOptionalSchema = number()
  .optional()
  .test(
    'not have state',
    'É necessário escolher o estado para escolher a cidade',
    (value, ctx) => ctx.parent.stateId && ctx.parent.stateId === 0
  )
  .test('cityId', 'É necessário escolher uma cidade', (value) => value !== 0);
