import { EnvironmentTypes } from 'src/enum/environment';

export const ENVIRONMENT_TYPES = [
  EnvironmentTypes.TENANT,
  EnvironmentTypes.COMPANY,
  EnvironmentTypes.WAREHOUSE,
];

export const ENVIRONMENT_TYPES_LABELED = [
  {
    label: 'Empresa',
    icon: 'vaadin:office',
    value: EnvironmentTypes.TENANT,
  },
  {
    label: 'Matriz/Filial',
    icon: 'mdi:company',
    value: EnvironmentTypes.COMPANY,
  },
  {
    label: 'Armazém',
    icon: 'material-symbols:warehouse',
    value: EnvironmentTypes.WAREHOUSE,
  },
];
