import { MoverType } from 'src/enum/mover';

export const MOVER_TYPES = [
  MoverType.STOCK,
  MoverType.PICKING,
  MoverType.DISPATCH,
  MoverType.RECEIPT,
];

export const MOVER_TYPES_LABELED = [
  {
    label: 'Estoque',
    icon: 'material-symbols:shelves-outline',
    value: MoverType.STOCK,
  },
  {
    label: 'Picking',
    icon: 'lsicon:picking-outline',
    value: MoverType.PICKING,
  },
  {
    label: 'Retorno',
    icon: 'carbon:delivery-parcel',
    value: MoverType.DISPATCH,
  },
  {
    label: 'Recebimento',
    icon: 'icon-park-outline:delivery',
    value: MoverType.RECEIPT,
  },
];
