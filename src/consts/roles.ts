export const roleDefaultAtributes = {
  getRoles: false,
  getAccounts: false,
  getCompanies: false,
  getWarehouses: false,
  getTenants: false,
  getProducts: false,
  getIntegrations: false,
  getTags: false,
  manageProducts: false,
  manageTenants: false,
  manageRoles: false,
  manageAccounts: false,
  manageCompanies: false,
  manageWarehouses: false,
  manageIdentifiers: false,
  manageStock: false,
  manageIntegrations: false,
  manageTags: false,
};

export const roleLabels = {
  getRoles: 'Get Roles',
  getAccounts: 'Get Accounts',
  getCompanies: 'Get Companies',
  getWarehouses: 'Get Warehouses',
  getTenants: 'Get Tenants',
  getProducts: 'Get Products',
  getIntegrations: 'Get Integrations',
  getTags: 'Get Tags',
  manageProducts: 'Manage Products',
  manageTenants: 'Manage Tenants',
  manageRoles: 'Manage Roles',
  manageAccounts: 'Manage Accounts',
  manageCompanies: 'Manage Companies',
  manageWarehouses: 'Manage Warehouses',
  manageIdentifiers: 'Manage Identifiers',
  manageStock: 'Manage Stock',
  manageIntegrations: 'Manage Integrations',
  manageTags: 'Manage Tags',
};
