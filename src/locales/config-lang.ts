'use client';

import merge from 'lodash/merge';
// date fns
import { ptBR as ptBRAdapter } from 'date-fns/locale';

// core (MUI)
import { ptBR as ptBRCore } from '@mui/material/locale';
// date pickers (MUI)
import { ptBR as ptBRDate } from '@mui/x-date-pickers/locales';
// data grid (MUI)
import { ptBR as ptBRDataGrid } from '@mui/x-data-grid/locales';

// PLEASE REMOVE `LOCAL STORAGE` WHEN YOU CHANGE SETTINGS.
// ----------------------------------------------------------------------

export const allLangs = [
  {
    label: 'Português',
    value: 'pt',
    systemValue: merge(ptBRDate, ptBRDataGrid, ptBRCore),
    adapterLocale: ptBRAdapter,
    icon: 'flagpack:br',
    numberFormat: {
      code: 'pt-BR',
      currency: 'BRL',
    },
  },
  // {
  //   label: 'English',
  //   value: 'en',
  //   systemValue: merge(enUSDate, enUSDataGrid, enUSCore),
  //   adapterLocale: enUSAdapter,
  //   icon: 'flagpack:gb-nir',
  //   numberFormat: {
  //     code: 'en-US',
  //     currency: 'USD',
  //   },
  // },
];

export const defaultLang = allLangs[0]; // English

// GET MORE COUNTRY FLAGS
// https://icon-sets.iconify.design/flagpack/
// https://www.dropbox.com/sh/nec1vwswr9lqbh9/AAB9ufC8iccxvtWi3rzZvndLa?dl=0
