import axios, { AxiosError, AxiosRequestConfig } from 'axios';

import { pathsBackend } from 'src/routes/paths-backend';

import { HOST_API } from 'src/config-global';
// eslint-disable-next-line import/no-cycle
// eslint-disable-next-line import/no-cycle
import { setAccessToken, getRefreshToken, setRefreshToken } from 'src/auth/context/jwt/utils';

import { IAuthRefresh } from 'src/types/auth';

import AppError from './error';

// ----------------------------------------------------------------------

type PromiseType = {
  onSuccess: (token: string) => void;
  onFailure: (error: AxiosError) => void;
};

// ----------------------------------------------------------------------

const axiosInstance = axios.create({ baseURL: HOST_API });

// ----------------------------------------------------------------------

const failedQueue: PromiseType[] = [];
let isRefreshing = false;

const interceptTokenManager = axiosInstance.interceptors.response.use(
  (response) => response,
  async (requestError) => {
    if (requestError?.response?.status === 401) {
      const refreshToken = getRefreshToken() || '';

      if (!refreshToken) {
        return Promise.reject(requestError);
      }

      const originalRequestConfig = requestError.config;

      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({
            onSuccess: (token: string) => {
              originalRequestConfig.headers = {
                Authorization: `Bearer ${token}`,
              };
              resolve(axiosInstance(originalRequestConfig));
            },
            onFailure: (error: AxiosError) => {
              reject(error);
            },
          });
        });
      }

      isRefreshing = true;

      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        try {
          const {
            data: {
              tokens: { accessToken: newAccessToken, refreshToken: newRefreshToken },
            },
          } = await axios.post<IAuthRefresh>(HOST_API + pathsBackend.auth.refreshTokens, {
            refreshToken,
          });

          setRefreshToken(newRefreshToken);
          setAccessToken(newAccessToken);

          if (originalRequestConfig.data) {
            originalRequestConfig.data = JSON.parse(originalRequestConfig.data);
          }

          originalRequestConfig.headers = {
            Authorization: `Bearer ${newAccessToken}`,
          };

          axiosInstance.defaults.headers.common.Authorization = `Bearer ${newAccessToken}`;

          failedQueue.forEach((request) => {
            request.onSuccess(newAccessToken);
          });

          resolve(axiosInstance(originalRequestConfig));
        } catch (error: any) {
          failedQueue.forEach((request) => {
            request.onFailure(error);
          });

          reject(error);
        } finally {
          isRefreshing = false;
        }
      });
    }

    if (requestError.response && requestError.response.data) {
      return Promise.reject(
        new AppError(requestError.response.data.message, requestError.response.status)
      );
    }
    return Promise.reject(requestError);
  }
);

axios.interceptors.response.eject(interceptTokenManager);

export default axiosInstance;

// ----------------------------------------------------------------------

export const fetcher = async (args: string | [string, AxiosRequestConfig]) => {
  const [url, config] = Array.isArray(args) ? args : [args];

  const res = await axiosInstance.get(url, { ...config });

  return res.data;
};
