/* eslint-disable consistent-return */

const fMaskPhone = (phone?: string | null) => {
  if (!phone) return;

  phone = phone.replace(/[^\d]/g, '');

  // formata o Phone com pontos, barra e traço
  if (phone.length === 11) {
    return `(${phone.substring(0, 2)}) ${phone.substring(2, 7)}-${phone.substring(7)}`;
  }

  return `(${phone.substring(0, 2)}) ${phone.substring(2, 6)}-${phone.substring(6)}`;
};

const fClearValue = (value?: string | null) => {
  if (!value) return '';

  const cleanValue = value.replace(/\D+/g, '').split('');

  return cleanValue.join('');
};

const fMaskCompletePhone = (phone?: string | null) => {
  if (!phone) return;

  const isBrazilPhone = phone.startsWith('55');

  phone = phone.replace(/[^\d]/g, '');

  if (isBrazilPhone) {
    if (phone.length === 12) {
      // like 9999-9999
      return `+${phone.substring(0, 2)} ${phone.substring(2, 4)} ${phone.substring(
        4,
        8
      )}-${phone.substring(8)}`;
    }

    if (phone.length === 13) {
      // like 99999-9999
      return `+${phone.substring(0, 2)} ${phone.substring(2, 4)} ${phone.substring(
        4,
        9
      )}-${phone.substring(9)}`;
    }
  }

  return phone;
};

export { fMaskPhone, fClearValue, fMaskCompletePhone };
