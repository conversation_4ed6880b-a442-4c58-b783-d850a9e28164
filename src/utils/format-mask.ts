/* eslint-disable consistent-return */

export const fMaskCNPJ = (cnpj?: string | null) => {
  if (!cnpj) return;

  // remove todos os caracteres não numéricos do CNPJ
  cnpj = cnpj.replace(/[^\d]/g, '');

  // formata o CNPJ com pontos, barra e traço
  return `${cnpj.substring(0, 2)}.${cnpj.substring(2, 5)}.${cnpj.substring(5, 8)}/${cnpj.substring(
    8,
    12
  )}-${cnpj.substring(12)}`;
};

export const fMaskCEP = (cep?: string | null) => {
  if (!cep) return;

  cep = cep.replace(/[^\d]/g, '');

  // formata o CEP com pontos, barra e traço
  return `${cep.substring(0, 2)}.${cep.substring(2, 5)}-${cep.substring(5, 8)}`;
};

export const fMaskCPF = (cpf?: string | null) => {
  if (!cpf) return;

  cpf = cpf.replace(/[^\d]/g, '');

  // formata o CPF com pontos, barra e traço
  return `${cpf.substring(0, 3)}.${cpf.substring(3, 6)}.${cpf.substring(6, 9)}-${cpf.substring(
    9,
    12
  )}`;
};

export const fMaskCPFCNPJ = (value: string | undefined | null) => {
  if (!value) return;

  const cleanedValue = value.replace(/[^\d]/g, '');

  if (cleanedValue.length === 11) {
    return fMaskCPF(cleanedValue);
  }

  return fMaskCNPJ(cleanedValue);
};

export const fMaskRG = (rg?: string | null) => {
  if (!rg) return;

  rg = rg.replace(/[^\d]/g, '');

  // formata o RG com pontos, barra e traço
  return `${rg.substring(0, 1)}.${rg.substring(1, 4)}.${rg.substring(4, 7)}`;
};

export const fMaskPhone = (phone?: string | null) => {
  if (!phone) return;

  phone = phone.replace(/[^\d]/g, '');

  // formata o Phone com pontos, barra e traço
  if (phone.length === 11) {
    return `(${phone.substring(0, 2)}) ${phone.substring(2, 7)}-${phone.substring(7)}`;
  }
  return `(${phone.substring(0, 2)}) ${phone.substring(2, 6)}-${phone.substring(6)}`;
};

export const fMaskPispasep = (pispasep?: string | null) => {
  if (!pispasep) return;

  pispasep = pispasep.replace(/[^\d]/g, '');

  // Formata o PIS/PASEP com pontos e traço
  return `${pispasep.substring(0, 3)}.${pispasep.substring(3, 8)}.${pispasep.substring(
    8,
    10
  )}-${pispasep.substring(10)}`;
};

export const fUSDToBRL = (numberBrl?: string | number | null) => {
  if (!numberBrl) return '';

  return parseFloat(String(numberBrl)).toLocaleString('pt-BR', { minimumFractionDigits: 2 });
};

export const fBRLToUSD = (numberUsd?: string | number | null) => {
  if (!numberUsd) return '';

  return parseFloat(String(numberUsd).replace('.', '').replace(',', '.')).toFixed(2);
};
