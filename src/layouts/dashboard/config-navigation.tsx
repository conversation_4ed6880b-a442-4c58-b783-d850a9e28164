import { useMemo } from 'react';

import { paths } from 'src/routes/paths';

import { useTranslate } from 'src/locales';
import { AccountRole } from 'src/enum/account';

import SvgColor from 'src/components/svg-color';
import { NavPropsData } from 'src/components/nav-section';

// ----------------------------------------------------------------------

const icon = (name: string) => (
  <SvgColor src={`/assets/icons/navbar/${name}.svg`} sx={{ width: 1, height: 1 }} />
  // OR
  // <Iconify icon="fluent:mail-24-filled" />
  // https://icon-sets.iconify.design/solar/
  // https://www.streamlinehq.com/icons
);

const ICONS = {
  tag: icon('ic_tag'),
  admin: icon('ic_admin'),
  tenant: icon('ic_tenant'),
  company: icon('ic_company'),
  account: icon('ic_account'),
  product: icon('ic_product'),
  dashboard: icon('ic_dashboard'),
  movers: icon('ic_movers'),
  packaging: icon('ic_packaging'),
  action: icon('ic_action'),
};

// ----------------------------------------------------------------------

export function useNavData() {
  const { t } = useTranslate();

  const data: NavPropsData[] = useMemo(
    () => [
      // OVERVIEW
      // ----------------------------------------------------------------------
      {
        subheader: t('overview'),
        items: [
          {
            title: 'Início',
            icon: ICONS.dashboard,
            path: paths.dashboard.root,
          },
        ],
      },

      // MANAGEMENT
      // ----------------------------------------------------------------------
      {
        subheader: t('management'),
        items: [
          // ACCOUNT
          {
            title: 'Usuários',
            path: paths.dashboard.account.root,
            icon: ICONS.account,
          },
          // FILIAIS
          {
            title: 'Filiais/matrizes',
            path: paths.dashboard.company.root,
            icon: ICONS.company,
          },
          // PRODUCTS
          {
            title: 'Produtos',
            path: paths.dashboard.product.root,
            icon: ICONS.product,
          },
          // TAGS
          {
            title: 'Modelos de Etiquetas',
            path: paths.dashboard.tags.root,
            icon: ICONS.tag,
          },
          {
            title: 'Movimentadores',
            path: paths.dashboard.movers.root,
            icon: ICONS.movers,
          },
          {
            title: 'Embalagens',
            path: paths.dashboard.packaging.root,
            icon: ICONS.packaging,
          },
          {
            title: 'Ações',
            path: paths.dashboard.actions.root,
            icon: ICONS.action,
          },
        ],
      },

      // ADMIN
      // ----------------------------------------------------------------------
      {
        subheader: 'Console',
        items: [
          {
            title: 'Console',
            path: paths.dashboard.admin.tenant.root,
            icon: ICONS.admin,
            // TODO: CHANGE THIS EARLY TO SUPERUSER
            isSuper: true,
            roles: [AccountRole.ADMIN],
            children: [
              // TENANTS
              {
                title: 'Empresas',
                icon: ICONS.tenant,
                path: paths.dashboard.admin.tenant.root,
              },
            ],
          },
        ],
      },
    ],
    [t]
  );

  return data;
}
