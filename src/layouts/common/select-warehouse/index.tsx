'use client';

import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Skeleton from '@mui/material/Skeleton';

import { useWarehouseContext } from 'src/warehouse';

export default function SelectWarehouse() {
  const { warehouses, currentWarehouse, changeWarehouse } = useWarehouseContext();

  return (
    <Select
      value={currentWarehouse?.id ?? ''}
      onChange={(event) => {
        changeWarehouse(event.target.value);
      }}
      sx={{
        '&.MuiOutlinedInput-root': {
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: 'transparent',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: 'transparent',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: 'transparent',
          },
        },
        minWidth: 100,
      }}
    >
      <MenuItem value="" disabled>
        Selecione um armazém
      </MenuItem>

      {warehouses?.map((w) => (
        <MenuItem value={w.id} key={`warehouse-menu-item-${w.id}`}>
          {w.name}
        </MenuItem>
      ))}
    </Select>
  );
}

export function SelectWarehouseSkeleton() {
  return <Skeleton variant="rounded" width={250} />;
}
