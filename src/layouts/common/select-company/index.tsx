'use client';

import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Skeleton from '@mui/material/Skeleton';

import { useCompanyContext } from 'src/company/hooks';

export default function SelectCompany() {
  const companyContext = useCompanyContext();
  const { companies, currentCompany, changeCompany } = companyContext;

  return (
    <Select
      value={currentCompany?.id ?? ''}
      onChange={(event) => {
        changeCompany(event.target.value);
      }}
      sx={{
        '&.MuiOutlinedInput-root': {
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: 'transparent',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: 'transparent',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: 'transparent',
          },
        },
        minWidth: 100,
      }}
    >
      <MenuItem value="0" disabled selected>
        Selecione uma filial
      </MenuItem>

      {companies.map((c) => (
        <MenuItem value={c.id} key={`company-menu-item-${c.id}`}>
          {c.name}
        </MenuItem>
      ))}
    </Select>
  );
}

export function SelectCompanySkeleton() {
  return <Skeleton variant="rounded" width={250} />;
}
