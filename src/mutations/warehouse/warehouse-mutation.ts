import { enqueueSnackbar } from 'notistack';
import { useMutation, useQueryClient, UseMutationOptions } from '@tanstack/react-query';

import { WarehouseRepository } from 'src/repositories/warehouse';

export type IMutationProps = {
  errorMessage?: string;
  successMessage?: string;
  callbackFn?: VoidFunction;
  fallbackFn?: VoidFunction;
};

export function useWarehouseCreateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: WarehouseRepository.create,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['companies', variables.companyId, 'warehouses', 'list'],
      });

      enqueueSnackbar(options?.successMessage ?? 'Armazém criado com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao criar o armazém! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}

export function useWarehouseUpdateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: WarehouseRepository.update,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['companies', variables.companyId, 'warehouses', 'list'],
      });
      await queryClient.invalidateQueries({
        queryKey: [
          'companies',
          variables.companyId,
          'warehouses',
          'details',
          variables.warehouseId,
        ],
      });

      enqueueSnackbar(options?.successMessage ?? 'Armazém alterado com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao alterar o armazém! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}

export function useWarehouseDeleteMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: WarehouseRepository.delete,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['companies', variables.companyId, 'warehouses', 'list'],
      });
      queryClient.removeQueries({
        queryKey: [
          'companies',
          variables.companyId,
          'warehouses',
          'details',
          variables.warehouseId,
        ],
      });

      enqueueSnackbar(options?.successMessage ?? 'Armazém removido com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao remover o armazém! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}
