import { enqueueSnackbar } from 'notistack';
import { useMutation, useQueryClient, UseMutationOptions } from '@tanstack/react-query';

import { ProductRepository } from 'src/repositories/product';

export type IMutationProps = {
  errorMessage?: string;
  successMessage?: string;
  callbackFn?: VoidFunction;
  fallbackFn?: VoidFunction;
};

export function useProductCreateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ProductRepository.create,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['products', 'list'],
      });

      enqueueSnackbar(options?.successMessage ?? 'Produto criado com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao criar o produto! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}

export function useProductUpdateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ProductRepository.update,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['products', 'list'],
      });
      await queryClient.invalidateQueries({
        queryKey: ['products', 'details', variables.productId],
      });

      enqueueSnackbar(options?.successMessage ?? 'Produto alterado com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao alterar o produto! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}

export function useProductDeleteMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ProductRepository.delete,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['products', 'list'],
      });
      queryClient.removeQueries({
        queryKey: ['products', 'details', variables.productId],
      });

      enqueueSnackbar(options?.successMessage ?? 'Produto removido com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao remover o produto! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}
