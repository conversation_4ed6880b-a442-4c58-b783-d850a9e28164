import { enqueueSnackbar } from 'notistack';
import { useMutation, useQueryClient, UseMutationOptions } from '@tanstack/react-query';

import { AccountRepository } from 'src/repositories/account';

export type IMutationProps = {
  errorMessage?: string;
  successMessage?: string;
  callbackFn?: VoidFunction;
  fallbackFN?: VoidFunction;
};

export function useAccountCreateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: AccountRepository.create,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ['accounts', 'list'] });

      enqueueSnackbar(options?.successMessage ?? 'Usuário criado com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao criar o usuário! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFN) {
        options.fallbackFN();
      }
    },
  });
}

export function useAccountUpdateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: AccountRepository.update,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ['accounts', 'list'] });
      await queryClient.invalidateQueries({
        queryKey: ['accounts', 'details', variables.accountId],
      });

      enqueueSnackbar(options?.successMessage ?? 'Usuário alterado com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao alterar o usuário! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFN) {
        options.fallbackFN();
      }
    },
  });
}

export function useAccountDeleteMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: AccountRepository.delete,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ['accounts', 'list'] });
      queryClient.removeQueries({
        queryKey: ['accounts', 'details', variables.accountId],
      });

      enqueueSnackbar(options?.successMessage ?? 'Usuário removido com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao remover o usuário! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFN) {
        options.fallbackFN();
      }
    },
  });
}
