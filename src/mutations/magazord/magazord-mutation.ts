import { enqueueSnackbar } from 'notistack';
import { useMutation, UseMutationOptions } from '@tanstack/react-query';

import { MagazordRepository } from 'src/repositories/integrations/magazord';

export type IMutationProps = {
  errorMessage?: string;
  successMessage?: string;
  callbackFn?: VoidFunction;
  fallbackFn?: VoidFunction;
};

export function useMagazordSyncProductsMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions<unknown, Error>
) {
  return useMutation({
    mutationFn: MagazordRepository.syncProducts,

    onSuccess: async (data, variables, context) => {
      enqueueSnackbar(options?.successMessage ?? `Sincronização de produtos em andamento!`);

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message ||
        'Ops! Tivemos problemas ao sincronizar os produtos! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
    ...mutationOptions,
  });
}
