import { enqueueSnackbar } from 'notistack';
import { useMutation, useQueryClient, UseMutationOptions } from '@tanstack/react-query';

import MagazordShopRepository from 'src/repositories/integrations/magazord/magazord-shop.repository';

import { IMagazordShopEditForm } from 'src/types/integrations/magazord/shop';

export type IMutationProps = {
  errorMessage?: string;
  successMessage?: string;
  callbackFn?: VoidFunction;
  fallbackFn?: VoidFunction;
};

export function useMagazordShopActiveMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions<unknown, Error, { shopId: string; active: boolean }>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: MagazordShopRepository.active,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ['magazord', 'shops', 'list'] });

      enqueueSnackbar(
        options?.successMessage ??
          `Loja ${variables.active ? 'ativada' : 'desativada'} com sucesso!`
      );

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message ||
        'Ops! Tivemos problemas ao ajustar a ativação da loja! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
    ...mutationOptions,
  });
}

export function useMagazordShopUpdateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions<
    unknown,
    Error,
    { shopId: string; updatedShop: IMagazordShopEditForm }
  >
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: MagazordShopRepository.update,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ['magazord', 'shops', 'list'] });

      enqueueSnackbar(options?.successMessage ?? 'Loja atualizada com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao atualizar a loja! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
    ...mutationOptions,
  });
}
