import { enqueueSnackbar } from 'notistack';
import { useMutation, UseMutationOptions } from '@tanstack/react-query';

import MovementRepository from 'src/repositories/movement/movement';

export type IMutationProps = {
  errorMessage?: string;
  successMessage?: string;
  callbackFn?: VoidFunction;
  fallbackFn?: VoidFunction;
};

export function usePartialMoveProductMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  return useMutation({
    mutationFn: MovementRepository.partialMoveProduct,

    onSuccess: async (data, variables, context) => {
      enqueueSnackbar(options?.successMessage ?? 'Produto movimentado com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message ||
        'Ops! Tivemos problemas ao movimentar o produto! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}

export function useTotalMoveProductMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  return useMutation({
    mutationFn: MovementRepository.totalMoveProduct,

    onSuccess: async (data, variables, context) => {
      enqueueSnackbar(options?.successMessage ?? 'Produto movimentado com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message ||
        'Ops! Tivemos problemas ao movimentar o produto! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}

export function useAllMoveProductMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  return useMutation({
    mutationFn: MovementRepository.allMoveProduct,

    onSuccess: async (data, variables, context) => {
      enqueueSnackbar(options?.successMessage ?? 'Produto movimentado com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message ||
        'Ops! Tivemos problemas ao movimentar o produto! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}
