import { enqueueSnackbar } from 'notistack';
import { useMutation, useQueryClient, UseMutationOptions } from '@tanstack/react-query';

import { PackagingRepository } from 'src/repositories/packaging';

export type IMutationProps = {
  errorMessage?: string;
  successMessage?: string;
  callbackFn?: VoidFunction;
  fallbackFn?: VoidFunction;
};

export function usePackagingCreateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: PackagingRepository.create,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['packagings', 'list'],
      });

      enqueueSnackbar(options?.successMessage ?? 'Embalagem criada com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao criar a embalagem! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}

export function usePackagingUpdateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: PackagingRepository.update,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['packagings', 'list'],
      });

      enqueueSnackbar(options?.successMessage ?? 'Embalagem atualizada com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message ||
        'Ops! Tivemos problemas ao atualizar a embalagem! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}

export function usePackagingDeleteMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: PackagingRepository.delete,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['packagings', 'list'],
      });

      enqueueSnackbar(options?.successMessage ?? 'Embalagem excluída com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message ||
        'Ops! Tivemos problemas ao excluir a embalagem! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}
