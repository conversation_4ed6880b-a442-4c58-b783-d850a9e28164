import { enqueueSnackbar } from 'notistack';
import { useRouter } from 'next/navigation';
import { useMutation, UseMutationOptions } from '@tanstack/react-query';

import { paths } from 'src/routes/paths';

// eslint-disable-next-line import/no-cycle
import AuthRepository from 'src/repositories/auth-repository';

import {
  IAuthLogin,
  IAuthLogout,
  IAuthRefresh,
  IAuthLoginParams,
  IAuthRefreshParams,
  IAuthResetPasswordParams,
  IAuthForgotPasswordParams,
} from 'src/types/auth';

type MutationProps = {
  errorMessage?: string;
  successMessage?: string;
  callbackFn?: VoidFunction;
  fallbackFN?: VoidFunction;
};

export function useLogin(mutationProps?: UseMutationOptions<IAuthLogin, Error, IAuthLoginParams>) {
  return useMutation({
    mutationFn: AuthRepository.login,
    ...mutationProps,
  });
}

export function useLogout(mutationProps?: UseMutationOptions<unknown, Error, IAuthLogout>) {
  return useMutation({
    mutationFn: AuthRepository.logout,
    ...mutationProps,
  });
}

export function useRefreshTokens(
  mutationProps?: UseMutationOptions<IAuthRefresh, Error, IAuthRefreshParams>
) {
  return useMutation({
    mutationFn: AuthRepository.refreshToken,
    ...mutationProps,
  });
}

export function useResetPassword(
  mutationProps?: UseMutationOptions<unknown, Error, IAuthResetPasswordParams>,
  options?: MutationProps
) {
  return useMutation({
    mutationFn: AuthRepository.resetPassword,
    onSuccess: (data, variables, context) => {
      enqueueSnackbar(options?.successMessage ?? 'Senha alterada com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      enqueueSnackbar(options?.errorMessage ?? 'Ops! Tivemos problemas para alterar sua senha!', {
        variant: 'error',
      });

      if (options?.fallbackFN) {
        options.fallbackFN();
      }
    },
    ...mutationProps,
  });
}

export function useForgotPassword(
  mutationProps?: UseMutationOptions<unknown, Error, IAuthForgotPasswordParams>,
  options?: MutationProps
) {
  const { replace } = useRouter();

  return useMutation({
    mutationFn: AuthRepository.forgotPassword,
    onSuccess: (data, variables, context) => {
      enqueueSnackbar(
        options?.successMessage ??
          'Um link foi enviado para seu email. Com ele, agora você pode redefinir sua senha!'
      );

      if (options?.callbackFn) {
        options.callbackFn();
      }

      replace(paths.auth.jwt.login);
    },

    onError: (error, variables, context) => {
      enqueueSnackbar(
        options?.errorMessage ??
          'Ops! Tivemos problemas para enviar o link de recuperação de senha!',
        {
          variant: 'error',
        }
      );

      if (options?.fallbackFN) {
        options.fallbackFN();
      }
    },
    ...mutationProps,
  });
}
