import { enqueueSnackbar } from 'notistack';
import { useMutation, useQueryClient, UseMutationOptions } from '@tanstack/react-query';

import MoverRepository from 'src/repositories/warehouse/mover-repository';

export type IMutationProps = {
  errorMessage?: string;
  successMessage?: string;
  callbackFn?: VoidFunction;
  fallbackFN?: VoidFunction;
};

export function useMoverCreateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: MoverRepository.create,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['companies', variables.companyId, 'movers', 'list'],
      });

      enqueueSnackbar(options?.successMessage ?? 'Movimentador criado com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message ||
        'Ops! Tivemos problemas ao criar o movimentador! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFN) {
        options.fallbackFN();
      }
    },
  });
}

export function useMoverUpdateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: MoverRepository.update,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['companies', variables.companyId, 'movers', 'list'],
      });

      enqueueSnackbar(options?.successMessage ?? 'Movimentador alterado com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message ||
        'Ops! Tivemos problemas ao alterar o movimentador! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFN) {
        options.fallbackFN();
      }
    },
  });
}

export function useMoverDeleteMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: MoverRepository.delete,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['companies', variables.companyId, 'movers', 'list'],
      });

      enqueueSnackbar(options?.successMessage ?? 'Movimentador removido com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message ||
        'Ops! Tivemos problemas ao remover o movimentador! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFN) {
        options.fallbackFN();
      }
    },
  });
}

export function useReplaceMoverMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: MoverRepository.replaceMover,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['companies', variables.companyId, 'movers', 'list'],
      });

      enqueueSnackbar(options?.successMessage ?? 'Movimentador movimentado com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message ||
        'Ops! Tivemos problemas ao movimentar o movimentador! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });
      if (options?.fallbackFN) {
        options.fallbackFN();
      }
    },
  });
}
