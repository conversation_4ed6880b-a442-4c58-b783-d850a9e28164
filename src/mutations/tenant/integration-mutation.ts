import { enqueueSnackbar } from 'notistack';
import { useMutation, useQueryClient, UseMutationOptions } from '@tanstack/react-query';

import IntegrationRepository from 'src/repositories/tenant/integration-repository';

export type IMutationProps = {
  errorMessage?: string;
  successMessage?: string;
  callbackFn?: VoidFunction;
  fallbackFn?: VoidFunction;
};

export function useIntegrationCreateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: IntegrationRepository.create,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ['integrations', 'list'] });

      enqueueSnackbar(options?.successMessage ?? 'Integração criada com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message ||
        'Ops! Tivemos problemas ao criar a integração! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}

export function useIntegrationUpdateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: IntegrationRepository.update,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ['integrations', 'list'] });
      await queryClient.invalidateQueries({
        queryKey: ['integrations', 'details', variables.integrationId],
      });

      enqueueSnackbar(options?.successMessage ?? 'Integração alterada com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message ||
        'Ops! Tivemos problemas ao alterar a integração! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}

export function useIntegrationDeleteMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: IntegrationRepository.delete,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ['integrations', 'list'] });
      queryClient.removeQueries({
        queryKey: ['integrations', 'details', variables.integrationId],
      });

      enqueueSnackbar(options?.successMessage ?? 'Integração removida com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message ||
        'Ops! Tivemos problemas ao remover a integração! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}

export function useIntegregrationSyncMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: IntegrationRepository.sync,
    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ['integrations', 'list'] });
      await queryClient.invalidateQueries({
        queryKey: ['integrations', 'details', variables.integrationId],
      });

      enqueueSnackbar(options?.successMessage ?? 'Integração sincronizada com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },
    onError: (error, variables, context) => {
      const errorMessage =
        error.message ||
        'Ops! Tivemos problemas ao sincronizar a integração! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}
