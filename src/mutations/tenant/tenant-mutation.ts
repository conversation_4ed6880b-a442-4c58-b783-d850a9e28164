import { enqueueSnackbar } from 'notistack';
import { useMutation, useQueryClient, UseMutationOptions } from '@tanstack/react-query';

import { TenantRepository } from 'src/repositories/tenant';

export type IMutationProps = {
  errorMessage?: string;
  successMessage?: string;
  callbackFn?: VoidFunction;
  fallbackFn?: VoidFunction;
};

export function useTenantCreateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: TenantRepository.create,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ['tenants', 'list'] });

      enqueueSnackbar(options?.successMessage ?? 'Empresa criada com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao criar a empresa! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}

export function useTenantUpdateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: TenantRepository.update,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ['tenants', 'list'] });
      await queryClient.invalidateQueries({
        queryKey: ['tenants', 'details', variables.tenantId],
      });

      enqueueSnackbar(options?.successMessage ?? 'Empresa alterada com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao alterar a empresa! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}

export function useTenantDeleteMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: TenantRepository.delete,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({ queryKey: ['tenants', 'list'] });
      queryClient.removeQueries({
        queryKey: ['tenants', 'details', variables.tenantId],
      });

      enqueueSnackbar(options?.successMessage ?? 'Empresa removida com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao remover a empresa! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}
