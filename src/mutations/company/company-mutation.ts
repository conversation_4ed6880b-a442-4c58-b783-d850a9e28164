import { enqueueSnackbar } from 'notistack';
import { useMutation, useQueryClient, UseMutationOptions } from '@tanstack/react-query';

import { CompanyRepository } from 'src/repositories/company';

export type IMutationProps = {
  errorMessage?: string;
  successMessage?: string;
  callbackFn?: VoidFunction;
  fallbackFn?: VoidFunction;
};

export function useCompanyCreateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: CompanyRepository.create,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['companies', 'list'],
      });

      enqueueSnackbar(options?.successMessage ?? 'Filial criada com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao criar a filial! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}

export function useCompanyUpdateMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: CompanyRepository.update,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['companies', 'list'],
      });
      await queryClient.invalidateQueries({
        queryKey: ['companies', 'details', variables.companyId],
      });

      enqueueSnackbar(options?.successMessage ?? 'Filial alterada com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao alterar a filial! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}

export function useCompanyDeleteMutation(
  options?: IMutationProps,
  mutationOptions?: UseMutationOptions
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: CompanyRepository.delete,

    onSuccess: async (data, variables, context) => {
      await queryClient.invalidateQueries({
        queryKey: ['companies', 'list'],
      });
      queryClient.removeQueries({
        queryKey: ['companies', 'details', variables.companyId],
      });

      enqueueSnackbar(options?.successMessage ?? 'Filial removida com sucesso!');

      if (options?.callbackFn) {
        options.callbackFn();
      }
    },

    onError: (error, variables, context) => {
      const errorMessage =
        error.message || 'Ops! Tivemos problemas ao remover a filial! Tente novamente mais tarde!';

      enqueueSnackbar(options?.errorMessage ?? errorMessage, {
        variant: 'error',
      });

      if (options?.fallbackFn) {
        options?.fallbackFn();
      }
    },
  });
}
