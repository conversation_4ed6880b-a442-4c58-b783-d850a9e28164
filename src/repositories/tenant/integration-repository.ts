import { pathsBackend } from 'src/routes/paths-backend';

import axiosInstance from 'src/utils/axios';

import { IIntegration, IIntegrationNewForm, IIntegrationUpdateForm } from 'src/types/integration';

export default class IntegrationRepository {
  static async getAll() {
    const { data } = await axiosInstance.get<IIntegration[]>(
      pathsBackend.tenant.integration.getAll()
    );

    return data;
  }

  static async create({ newIntegration }: { newIntegration: IIntegrationNewForm }) {
    const { data } = await axiosInstance.post<IIntegration>(
      pathsBackend.tenant.integration.create(),
      newIntegration
    );

    return data;
  }

  static async show({ integrationId }: { integrationId: string }) {
    const { data } = await axiosInstance.get<IIntegration>(
      pathsBackend.tenant.integration.show(integrationId)
    );

    return data;
  }

  static async update({
    integrationId,
    updatedIntegration,
  }: {
    integrationId: string;
    updatedIntegration: IIntegrationUpdateForm;
  }) {
    const { data } = await axiosInstance.put<IIntegration>(
      pathsBackend.tenant.integration.update(integrationId),
      updatedIntegration
    );

    return data;
  }

  static async delete({ integrationId }: { integrationId: string }) {
    const { data } = await axiosInstance.delete(
      pathsBackend.tenant.integration.delete(integrationId)
    );

    return data;
  }

  static async sync({ integrationId }: { integrationId: string }) {
    const { data } = await axiosInstance.post<IIntegration>(
      pathsBackend.tenant.integration.sync(integrationId)
    );

    return data;
  }
}
