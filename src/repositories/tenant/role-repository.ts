import { pathsBackend } from 'src/routes/paths-backend';

import axiosInstance from 'src/utils/axios';

export default class RoleRepository {
  static async getAll() {
    const { data } = await axiosInstance.get(pathsBackend.tenant.roles.getAll());

    return data;
  }

  static async create() {
    const { data } = await axiosInstance.post(pathsBackend.tenant.roles.create());

    return data;
  }

  static async show({ roleId }: { roleId: string }) {
    const { data } = await axiosInstance.get(pathsBackend.tenant.roles.show(roleId));

    return data;
  }

  static async update({ roleId }: { roleId: string }) {
    const { data } = await axiosInstance.put(pathsBackend.tenant.roles.update(roleId));

    return data;
  }

  static async delete({ roleId }: { roleId: string }) {
    const { data } = await axiosInstance.delete(pathsBackend.tenant.roles.delete(roleId));

    return data;
  }
}
