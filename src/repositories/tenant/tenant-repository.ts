import { pathsBackend } from 'src/routes/paths-backend';

import axiosInstance from 'src/utils/axios';

import { IWarehouse } from 'src/types/warehouse';
import { ITenant, ITenantNewForm, ITenantUpdateForm } from 'src/types/tenant';

export default class TenantRepository {
  static async getAll() {
    const { data } = await axiosInstance.get<ITenant[]>(pathsBackend.tenant.getAll);

    return data;
  }

  static async getAllWarehouse({ tenantId }: { tenantId: string }) {
    const { data } = await axiosInstance.get<{ companyName: string; warehouses: IWarehouse[] }[]>(
      pathsBackend.tenant.getAllWarehouse(tenantId)
    );

    return data;
  }

  static async create({ newTenant }: { newTenant: ITenantNewForm }) {
    const { data } = await axiosInstance.post<ITenant>(pathsBackend.tenant.create, newTenant);

    return data;
  }

  static async show({ tenantId }: { tenantId: string }) {
    const { data } = await axiosInstance.get<ITenant>(pathsBackend.tenant.show(tenantId));

    return data;
  }

  static async update({
    tenantId,
    updatedTenant,
  }: {
    tenantId: string;
    updatedTenant: ITenantUpdateForm;
  }) {
    const { data } = await axiosInstance.put<ITenant>(
      pathsBackend.tenant.update(tenantId),
      updatedTenant
    );

    return data;
  }

  static async delete({ tenantId }: { tenantId: string }) {
    const { data } = await axiosInstance.delete(pathsBackend.tenant.delete(tenantId));

    return data;
  }
}
