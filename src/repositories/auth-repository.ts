import axios from 'axios';

import { pathsBackend } from 'src/routes/paths-backend';

// eslint-disable-next-line import/no-cycle
import axiosInstance from 'src/utils/axios';

import { HOST_API } from 'src/config-global';

import {
  IAuthLogin,
  IAuthLogout,
  IAuthRefresh,
  IAuthMyAccount,
  IAuthLoginParams,
  IAuthRefreshParams,
  IAuthResetPasswordParams,
  IAuthForgotPasswordParams,
} from 'src/types/auth';

export default class AuthRepository {
  static async login(body: IAuthLoginParams) {
    const { data } = await axiosInstance.post<IAuthLogin>(pathsBackend.auth.login, body);

    return data;
  }

  static async logout(body: IAuthLogout) {
    const { data } = await axiosInstance.post(pathsBackend.auth.logout, body);

    return data;
  }

  static async myAccount() {
    const { data } = await axiosInstance.get<IAuthMyAccount>(pathsBackend.auth.myAccount);

    return data;
  }

  static async refreshToken(body: IAuthRefreshParams) {
    const { data } = await axios.post<IAuthRefresh>(
      `${HOST_API}${pathsBackend.auth.refreshTokens}`,
      body
    );

    return data;
  }

  static async changePassword() {
    const { data } = await axiosInstance.post(pathsBackend.auth.changePassword, {});

    return data;
  }

  static async forgotPassword(body: IAuthForgotPasswordParams) {
    const { data } = await axiosInstance.post(pathsBackend.auth.forgotPassword, body);

    return data;
  }

  static async resetPassword({ params, password }: IAuthResetPasswordParams) {
    const { data } = await axiosInstance.post(
      pathsBackend.auth.resetPassword,
      { password },
      { params }
    );

    return data;
  }
}
