import axiosInstance from 'src/utils/axios';

import { IMagazordShopList, IMagazordShopEditForm } from 'src/types/integrations/magazord/shop';

export default class MagazordShopRepository {
  static async getAll({
    integrationId,
  }: {
    integrationId: string;
  }): Promise<IMagazordShopList> {
    const { data } = await axiosInstance.get<IMagazordShopList>(
      `/v1/integrations/magazord/shops`
    );

    return data;
  }

  static async active({
    shopId,
    active,
  }: {
    shopId: string;
    active: boolean;
  }): Promise<IMagazordShopList> {
    const { data } = await axiosInstance.put<IMagazordShopList>(
      `/v1/integrations/magazord/shops/${shopId}/active`,
      {
        active,
      }
    );

    return data;
  }

  static async update({
    shopId,
    updatedShop,
  }: {
    shopId: string;
    updatedShop: IMagazordShopEditForm;
  }): Promise<IMagazordShopEditForm> {
    const { data } = await axiosInstance.put<IMagazordShopEditForm>(
      `/v1/integrations/magazord/shops/${shopId}`,
      {
        updatedShop,
      }
    );

    return data;
  }
}
