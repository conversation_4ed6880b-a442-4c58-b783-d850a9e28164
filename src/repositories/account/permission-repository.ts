import { pathsBackend } from 'src/routes/paths-backend';

import axiosInstance from 'src/utils/axios';

export default class AccountRepository {
  static async getAll({ accountId }: { accountId: string }) {
    const { data } = await axiosInstance.get(pathsBackend.account.permission.getAll(accountId));

    return data;
  }

  static async create({ accountId }: { accountId: string }) {
    const { data } = await axiosInstance.post(pathsBackend.account.permission.create(accountId));

    return data;
  }

  static async show({ accountId, permissionId }: { accountId: string; permissionId: string }) {
    const { data } = await axiosInstance.get(
      pathsBackend.account.permission.show(accountId, permissionId)
    );

    return data;
  }

  static async update({ accountId, permissionId }: { accountId: string; permissionId: string }) {
    const { data } = await axiosInstance.put(
      pathsBackend.account.permission.update(accountId, permissionId)
    );

    return data;
  }

  static async delete({ accountId, permissionId }: { accountId: string; permissionId: string }) {
    const { data } = await axiosInstance.delete(
      pathsBackend.account.permission.delete(accountId, permissionId)
    );

    return data;
  }
}
