import { pathsBackend } from 'src/routes/paths-backend';

import axiosInstance from 'src/utils/axios';

import { IPagination } from 'src/types/pagination';
import { IAccount, AccountList, IAccountData, IAccountQuery } from 'src/types/account';

export default class AccountRepository {
  static async getAll({ params }: { params?: IAccountQuery }) {
    const { data } = await axiosInstance.get<IPagination<AccountList>>(
      pathsBackend.account.getAll(),
      {
        params,
      }
    );

    return data;
  }

  static async create({ newAccount }: { newAccount: IAccountData }) {
    const { data } = await axiosInstance.post(pathsBackend.account.create(), newAccount);

    return data;
  }

  static async show({ accountId }: { accountId: string }) {
    const { data } = await axiosInstance.get<IAccount>(pathsBackend.account.show(accountId));

    return data;
  }

  static async update({
    accountId,
    updatedAccount,
  }: {
    accountId: string;
    updatedAccount: Partial<Omit<IAccountData, 'password'>>;
  }) {
    const { data } = await axiosInstance.put(
      pathsBackend.account.update(accountId),
      updatedAccount
    );

    return data;
  }

  static async delete({ accountId }: { accountId: string }) {
    const { data } = await axiosInstance.delete(pathsBackend.account.delete(accountId));

    return data;
  }
}
