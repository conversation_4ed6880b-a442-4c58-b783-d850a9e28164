import { pathsBackend } from 'src/routes/paths-backend';

import axiosInstance from 'src/utils/axios';

import { IWarehouse, IWarehouseData } from 'src/types/warehouse';

export default class WarehouseRepository {
  static async getAll({ companyId }: { companyId: string }) {
    const { data } = await axiosInstance.get<IWarehouse[]>(
      pathsBackend.warehouse.getAll(companyId)
    );

    return data;
  }

  static async create({
    companyId,
    newWarehouse,
  }: {
    companyId: string;
    newWarehouse: IWarehouseData;
  }) {
    const { data } = await axiosInstance.post<IWarehouse>(
      pathsBackend.warehouse.create(companyId),
      newWarehouse
    );

    return data;
  }

  static async show({ companyId, warehouseId }: { companyId: string; warehouseId: string }) {
    const { data } = await axiosInstance.get<IWarehouse>(
      pathsBackend.warehouse.show(companyId, warehouseId)
    );

    return data;
  }

  static async update({
    companyId,
    warehouseId,
    updatedWarehouse,
  }: {
    companyId: string;
    warehouseId: string;
    updatedWarehouse: Partial<IWarehouseData>;
  }) {
    const { data } = await axiosInstance.put<IWarehouse>(
      pathsBackend.warehouse.update(companyId, warehouseId),
      updatedWarehouse
    );

    return data;
  }

  static async delete({ companyId, warehouseId }: { companyId: string; warehouseId: string }) {
    const { data } = await axiosInstance.delete(
      pathsBackend.warehouse.delete(companyId, warehouseId)
    );

    return data;
  }
}
