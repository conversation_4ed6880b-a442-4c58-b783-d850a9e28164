import { pathsBackend } from 'src/routes/paths-backend';

import axiosInstance from 'src/utils/axios';

import { IPagination } from 'src/types/pagination';
import { IMover, MoverList, IMoverData, IMoverQuery } from 'src/types/mover';

export default class MoverRepository {
  static async create({
    companyId,
    warehouseId,
    newMover,
  }: {
    companyId: string;
    warehouseId: string;
    newMover: IMoverData;
  }) {
    const { data } = await axiosInstance.post<IMover>(
      pathsBackend.warehouse.mover.create(companyId, warehouseId),
      newMover
    );

    return data;
  }

  static async getAll({
    companyId,
    warehouseId,
    params,
  }: {
    companyId: string;
    warehouseId: string;
    params?: IMoverQuery;
  }) {
    const { data } = await axiosInstance.get<IPagination<MoverList>>(
      pathsBackend.warehouse.mover.getAll(companyId, warehouseId),
      {
        params,
      }
    );

    return data;
  }

  static async update({
    companyId,
    warehouseId,
    moverId,
    updatedMover,
  }: {
    companyId: string;
    warehouseId: string;
    moverId: string;
    updatedMover: Partial<IMoverData>;
  }) {
    const { data } = await axiosInstance.put(
      pathsBackend.warehouse.mover.update(companyId, warehouseId, moverId),
      updatedMover
    );

    return data;
  }

  static async delete({
    companyId,
    warehouseId,
    moverId,
  }: {
    companyId: string;
    warehouseId: string;
    moverId: string;
  }) {
    const { data } = await axiosInstance.delete(
      pathsBackend.warehouse.mover.delete(companyId, warehouseId, moverId)
    );

    return data;
  }

  static async replaceMover({
    companyId,
    warehouseId,
    moverId,
    toStockPositionId,
  }: {
    companyId: string;
    warehouseId: string;
    moverId: string;
    toStockPositionId: string | null;
  }) {
    const { data } = await axiosInstance.put(
      pathsBackend.warehouse.mover.replaceMover(companyId, warehouseId, moverId),
      {
        toStockPositionId,
      }
    );

    return data;
  }
}
