import { pathsBackend } from 'src/routes/paths-backend';

import axiosInstance from 'src/utils/axios';

import { ICompany, ICompanyData } from 'src/types/company';

export default class CompanyRepository {
  static async getAll() {
    const { data } = await axiosInstance.get<ICompany[]>(pathsBackend.company.getAll());

    return data;
  }

  static async create({ newCompany }: { newCompany: ICompanyData }) {
    const { data } = await axiosInstance.post<ICompany>(pathsBackend.company.create(), newCompany);

    return data;
  }

  static async show({ companyId }: { companyId: string }) {
    const { data } = await axiosInstance.get<ICompany>(pathsBackend.company.show(companyId));

    return data;
  }

  static async update({
    companyId,
    updatedCompany,
  }: {
    companyId: string;
    updatedCompany: Partial<ICompanyData>;
  }) {
    const { data } = await axiosInstance.put<ICompany>(
      pathsBackend.company.update(companyId),
      updatedCompany
    );

    return data;
  }

  static async delete({ companyId }: { companyId: string }) {
    const { data } = await axiosInstance.delete(pathsBackend.company.delete(companyId));

    return data;
  }
}
