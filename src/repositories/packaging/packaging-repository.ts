import { pathsBackend } from 'src/routes/paths-backend';

import axiosInstance from 'src/utils/axios';

import { IPagination, IPaginationParams } from 'src/types/pagination';
import {
  IPackaging,
  PackagingList,
  IPackagingNewForm,
  IPackagingUpdateForm,
} from 'src/types/packaging';

export default class PackagingRepository {
  static async getAll({ params }: { params: IPaginationParams }) {
    const { data } = await axiosInstance.get<IPagination<PackagingList>>(
      pathsBackend.packaging.getAll(),
      {
        params,
      }
    );

    return data;
  }

  static async create({ newPackaging }: { newPackaging: IPackagingNewForm }) {
    const { data } = await axiosInstance.post<IPackaging>(
      pathsBackend.packaging.create(),
      newPackaging
    );

    return data;
  }

  static async show({ packagingId }: { packagingId: string }) {
    const { data } = await axiosInstance.get<IPackaging>(pathsBackend.packaging.show(packagingId));

    return data;
  }

  static async update({
    packagingId,
    updatedPackaging,
  }: {
    packagingId: string;
    updatedPackaging: IPackagingUpdateForm;
  }) {
    const { data } = await axiosInstance.put<IPackaging>(
      pathsBackend.packaging.update(packagingId),
      updatedPackaging
    );

    return data;
  }

  static async delete({ packagingId }: { packagingId: string }) {
    const { data } = await axiosInstance.delete(pathsBackend.packaging.delete(packagingId));

    return data;
  }
}
