import { pathsBackend } from 'src/routes/paths-backend';

import axiosInstance from 'src/utils/axios';

import { IProduct, IProductNewForm, IProductUpdateForm } from 'src/types/product';
import { IPagination, IPaginationParams, IProductPagination } from 'src/types/pagination';

export default class ProductRepository {
  static async getAll({ params }: { params: IPaginationParams }) {
    const { data } = await axiosInstance.get<IPagination<IProductPagination>>(
      pathsBackend.product.getAll(),
      {
        params,
      }
    );

    return data;
  }

  static async create({ newTenant }: { newTenant: IProductNewForm }) {
    const { data } = await axiosInstance.post<IProduct>(pathsBackend.product.create(), newTenant);

    return data;
  }

  static async show({ productId }: { productId: string }) {
    const { data } = await axiosInstance.get<IProduct>(pathsBackend.product.show(productId));

    return data;
  }

  static async update({
    productId,
    updatedTenant,
  }: {
    productId: string;
    updatedTenant: IProductUpdateForm;
  }) {
    const { data } = await axiosInstance.put<IProduct>(
      pathsBackend.product.update(productId),
      updatedTenant
    );

    return data;
  }

  static async delete({ productId }: { productId: string }) {
    const { data } = await axiosInstance.delete(pathsBackend.product.delete(productId));

    return data;
  }
}
