import { pathsBackend } from 'src/routes/paths-backend';

import axiosInstance from 'src/utils/axios';

export type TagRepositoryTypes = {
  show: {
    tagId: string;
  };
};

export default class TagRepository {
  static async getAll() {
    const { data } = await axiosInstance.get(pathsBackend.tag.getAll());

    return data;
  }

  static async getState({ tagId }: TagRepositoryTypes['show']) {
    const { data } = await axiosInstance.get(pathsBackend.tag.show(tagId));

    return data;
  }
}
