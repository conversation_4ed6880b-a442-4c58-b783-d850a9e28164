import { pathsBackend } from 'src/routes/paths-backend';

import axiosInstance from 'src/utils/axios';

// TODO: TYPE FOR ENTIRE REPOSITORY
export default class StateRepository {
  static async getAll() {
    const { data } = await axiosInstance.get(pathsBackend.states.getAll);

    return data;
  }

  static async getState({ stateUf }: { stateUf: string }) {
    const { data } = await axiosInstance.get(pathsBackend.states.show(stateUf));

    return data;
  }
}
