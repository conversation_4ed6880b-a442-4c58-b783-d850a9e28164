import { pathsBackend } from 'src/routes/paths-backend';

import axiosInstance from 'src/utils/axios';

// TODO: TYPE FOR ENTIRE REPOSITORY
export default class CityRepository {
  static async getAll({ stateUf }: { stateUf: string }) {
    const { data } = await axiosInstance.get(pathsBackend.cities.getAll(stateUf));

    return data;
  }

  static async getCity({ stateUf, cityId }: { cityId: string; stateUf: string }) {
    const { data } = await axiosInstance.get(pathsBackend.cities.show(stateUf, cityId));

    return data;
  }
}
