import { pathsBackend } from 'src/routes/paths-backend';

import axiosInstance from 'src/utils/axios';

export default class MovementRepository {
  static async partialMoveProduct({
    productIdentifierId,
    fromIdentifierId,
    toIdentifierId,
    quantity,
  }: {
    productIdentifierId: string;
    fromIdentifierId: string;
    toIdentifierId: string;
    quantity: number;
  }) {
    const { data } = await axiosInstance.post(pathsBackend.movement.partialMoveProduct(), {
      productIdentifierId,
      fromIdentifierId,
      toIdentifierId,
      quantity,
    });

    return data;
  }

  static async totalMoveProduct({
    productIdentifierId,
    fromIdentifierId,
    toIdentifierId,
  }: {
    productIdentifierId: string;
    fromIdentifierId: string;
    toIdentifierId: string;
  }) {
    const { data } = await axiosInstance.post(pathsBackend.movement.totalMoveProduct(), {
      productIdentifierId,
      fromIdentifierId,
      toIdentifierId,
    });

    return data;
  }

  static async allMoveProduct({
    fromIdentifierId,
    toIdentifierId,
  }: {
    fromIdentifierId: string;
    toIdentifierId: string;
  }) {
    const { data } = await axiosInstance.post(pathsBackend.movement.allMoveProduct(), {
      fromIdentifierId,
      toIdentifierId,
    });

    return data;
  }
}
