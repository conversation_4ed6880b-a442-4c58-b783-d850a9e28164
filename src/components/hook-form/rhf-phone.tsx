// mask
import { IMaskMixin } from 'react-imask';
import { Controller, useFormContext } from 'react-hook-form';

// @mui
import TextField, { TextFieldProps } from '@mui/material/TextField';

// ----------------------------------------------------------------------

type IProps = {
  name: string;
};

type Props = IProps & TextFieldProps;

// @ts-ignore
const IMaskPhone = IMaskMixin(({ ...props }) => <TextField {...props} />);

export default function RHFPhone({ name, ...other }: Props) {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <IMaskPhone
          overwrite
          error={!!error}
          label="Telefone/celular"
          mask={
            field.value && field.value.length >= 6 && field.value[5] === '9'
              ? '(00) 00000-0000'
              : '(00) 0000-0000'
          }
          helperText={error?.message}
          {...other}
          {...field}
        />
      )}
    />
  );
}
