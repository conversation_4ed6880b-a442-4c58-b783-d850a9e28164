import { FormHTMLAttributes } from 'react';
import { UseFormReturn, FormProvider as Form } from 'react-hook-form';

// ----------------------------------------------------------------------

type Props = {
  children: React.ReactNode;
  methods: UseFormReturn<any>;
} & FormHTMLAttributes<HTMLFormElement>;

export default function FormProvider({ children, methods, ...formProps }: Props) {
  return (
    <Form {...methods}>
      <form {...formProps}>{children}</form>
    </Form>
  );
}
