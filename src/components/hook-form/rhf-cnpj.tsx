// form
import { IMaskMixin } from 'react-imask';
// mask
import { Controller, useFormContext } from 'react-hook-form';

// @mui
import TextField, { TextFieldProps } from '@mui/material/TextField';

// ----------------------------------------------------------------------

type IProps = {
  name: string;
};

type Props = IProps & TextFieldProps;

// @ts-ignore
const IMaskPhone = IMaskMixin(({ ...props }) => <TextField {...props} />);

export default function RHFCNPJ({ name, ...other }: Props) {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <IMaskPhone
          fullWidth
          overwrite
          label="CNPJ"
          error={!!error}
          mask="00.000.000/0000-00"
          helperText={error?.message}
          {...other}
          {...field}
        />
      )}
    />
  );
}
