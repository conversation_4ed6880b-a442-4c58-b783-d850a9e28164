// ----------------------------------------------------------------------

import { ICompany } from 'src/types/company';

export type ActionMapType<M extends { [index: string]: any }> = {
  [Key in keyof M]: M[Key] extends undefined
    ? {
        type: Key;
      }
    : {
        type: Key;
        payload: M[Key];
      };
};

export type CompanyType = null | ICompany;

export type CompanyStateType = {
  loading: boolean;
  companies: ICompany[];
  status?: CompanyStatusType;
  currentCompany: CompanyType;
};

// ----------------------------------------------------------------------

export type CompanyContextType = {
  loading?: boolean;
  companies: ICompany[];
  status: CompanyStatusType;
  currentCompany: CompanyType;
  changeCompany: (companyId: string) => void;
};

export enum CompanyStatusType {
  LOADED = 'LOADED',
  LOADING = 'LOADING',
}
