'use client';

import { SplashScreen } from 'src/components/loading-screen';

import { CompanyContext } from './company-context';

// ----------------------------------------------------------------------

type Props = {
  children: React.ReactNode;
};

export function CompanyConsumer({ children }: Props) {
  return (
    <CompanyContext.Consumer>
      {(company) => (company.loading ? <SplashScreen /> : children)}
    </CompanyContext.Consumer>
  );
}
