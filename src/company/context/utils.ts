/* eslint-disable import/no-cycle */

import axiosInstance from 'src/utils/axios';

import { TENANT_KEY } from 'src/auth/context/jwt/auth-provider';

import { COMPANY_KEY } from './company-provider';

export const getCompanyId = () => {
  const companyId = localStorage.getItem(COMPANY_KEY);

  return companyId;
};

export const setCompanyId = (companyId: string | null) => {
  if (companyId) {
    localStorage.setItem(COMPANY_KEY, String(companyId));

    axiosInstance.defaults.headers.common.companyId = companyId;
  } else {
    localStorage.removeItem(COMPANY_KEY);

    delete axiosInstance.defaults.headers.common.companyId;
  }
};

// --------------------------------------------------------------

export const getTenantId = () => {
  const tenantId = localStorage.getItem(TENANT_KEY);

  return tenantId;
};

export const setTenantId = (tenantId: string | null) => {
  if (tenantId) {
    localStorage.setItem(TENANT_KEY, String(tenantId));

    axiosInstance.defaults.headers.common.tenantId = tenantId;
  } else {
    localStorage.removeItem(TENANT_KEY);

    delete axiosInstance.defaults.headers.common.tenantId;
  }
};
