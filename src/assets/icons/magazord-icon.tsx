import React from 'react';

type Props = {
  width?: number;
  height?: number;
  fixedViewBox?: boolean;
};

export default function MagazordIcon({ width = 37, height = 37, fixedViewBox }: Props) {
  return (
    <svg
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={fixedViewBox ? `0 0 37 37` : `0 0 ${width} ${height}`}
    >
      <path
        fill="#16AB74"
        d="M23.14 26.971v3.505l-6.487 4.966v-5.355l5.035-3.797c.387-.293.968-.195 1.258.194a.746.746 0 01.194.487z"
      />
      <path
        fill="#1C8F4E"
        d="M16.653 35.442l-6.583-4.966v-3.505c0-.487.387-.877.968-.877.193 0 .387.098.484.195l5.034 3.798.097 5.355z"
      />
      <path
        fill="#EB599B"
        d="M27.497 2.434v3.213c0 .098-.097.195-.097.292l-10.747 8.082V8.763l9.198-7.01a.932.932 0 011.355 0c.194.194.29.39.29.681z"
      />
      <path
        fill="url(#paint0_linear_42_2)"
        d="M15.975 19.182a.536.536 0 00-.29-.487L4.162 9.932c-.774-.487-1.936-.293-2.42.584-.194.292-.29.584-.29.876V24.05l4.356 3.213v-9.64c0-.291.29-.486.581-.486.097 0 .194 0 .29.097l8.327 6.33c.29.194.678.194.871-.098.097-.098.097-.195.097-.39v-3.894z"
      />
      <path
        fill="url(#paint1_linear_42_2)"
        d="M28.949 9.932l-11.522 8.763a.536.536 0 00-.29.487v3.797c0 .292.29.584.58.584.098 0 .291 0 .388-.097l8.327-6.329c.193-.195.58-.097.774.097.097.098.097.195.097.292v9.737l4.26-3.213V11.392c0-.973-.871-1.752-1.84-1.655-.193 0-.484.097-.774.195z"
      />
      <path
        fill="#E52C46"
        d="M7.358 1.655c-.387-.292-1.065-.195-1.355.195-.097.195-.194.39-.194.584V9.25c0 .292.097.487.29.681l3.002 2.337c.29.195.58.098.774-.195 0-.097.097-.194.097-.292V9.834c0-.195.194-.39.388-.39.096 0 .096 0 .193.098l6.003 4.479V8.763L7.358 1.655z"
      />
      <defs>
        <linearGradient
          id="paint0_linear_42_2"
          x1="1.553"
          x2="15.997"
          y1="18.462"
          y2="18.462"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FDC418" />
          <stop offset="1" stopColor="#F6A217" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_42_2"
          x1="17.216"
          x2="31.66"
          y1="18.479"
          y2="18.479"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#0319F7" />
          <stop offset="1" stopColor="#2041F9" />
        </linearGradient>
      </defs>
    </svg>
  );
}
