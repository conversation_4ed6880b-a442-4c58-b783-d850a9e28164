'use client';

import { Button, Container } from '@mui/material';

import { paths } from 'src/routes/paths';

import { useBoolean } from 'src/hooks/use-boolean';

import { useTenantShowQuery } from 'src/queries/tenant';

import Iconify from 'src/components/iconify';
import CustomBreadcrumbs from 'src/components/custom-breadcrumbs/custom-breadcrumbs';

import { IntegrationsTable } from '../integrations-table-list';
import { IntegrationNewModal } from '../integration-new-modal';

type Props = {
  tenantId: string;
};

export function IntegrationsListView({ tenantId }: Props) {
  const newModal = useBoolean();

  const { data: tenant } = useTenantShowQuery(tenantId);

  return (
    <Container maxWidth={false}>
      <CustomBreadcrumbs
        heading="Integrações"
        links={[
          { name: 'Início', href: paths.dashboard.root },
          {
            name: 'Lista',
            href: paths.dashboard.admin.tenant.root,
          },
          { name: tenant.name },
        ]}
        sx={{
          mb: 3,
        }}
        action={
          <Button
            variant="contained"
            startIcon={<Iconify icon="mingcute:add-line" />}
            onClick={newModal.onTrue}
          >
            Adicionar Integração
          </Button>
        }
      />

      <IntegrationsTable tenant={tenant} />

      <IntegrationNewModal tenant={tenant} open={newModal.value} onClose={newModal.onFalse} />
    </Container>
  );
}
