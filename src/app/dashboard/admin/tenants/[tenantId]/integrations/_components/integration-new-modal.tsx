import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import { Stack } from '@mui/system';
import { LoadingButton } from '@mui/lab';
import { Button, Dialog, MenuItem, DialogTitle, DialogActions, DialogContent } from '@mui/material';

import { NewIntegrationSchema } from 'src/schemas/admin/tenant/integration';
import { useIntegrationCreateMutation } from 'src/mutations/tenant/integration-mutation';

import FormProvider from 'src/components/hook-form/form-provider';
import { RHFSelect, RHFSwitch, RHFTextField } from 'src/components/hook-form';

import { ITenant } from 'src/types/tenant';
import { IntegrationType } from 'src/types/integration';

import { MagazordBadge } from './badges';

type Props = {
  open: boolean;
  onClose: VoidFunction;
  tenant: ITenant;
};

type FormDataProps = {
  name: string;
  type: IntegrationType;
  content: { [key: string]: string };
  active: boolean;
  slug: string;
};

export function IntegrationNewModal({ onClose, open, tenant }: Props) {
  const defaultValues = {
    name: '',
    active: true,
  };

  const methods = useForm<FormDataProps>({
    resolver: yupResolver(NewIntegrationSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
    watch,
  } = methods;

  const { mutateAsync, isPending } = useIntegrationCreateMutation({
    callbackFn: () => {
      reset();
      onClose();
    },
  });

  const onSubmit = handleSubmit(async (newIntegration) => {
    mutateAsync({ newIntegration });
  });

  const type = watch('type');

  return (
    <Dialog
      fullWidth
      maxWidth={false}
      open={open}
      onClose={() => {
        reset();
        onClose();
      }}
      PaperProps={{
        sx: { maxWidth: 720 },
      }}
    >
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <DialogTitle>Nova Integração</DialogTitle>

        <DialogContent>
          <Stack spacing={3} sx={{ py: 2 }}>
            <RHFTextField name="name" label="Nome" fullWidth />

            <RHFTextField name="slug" label="Slug" fullWidth />

            <RHFSelect name="type" label="Integração">
              <MenuItem key={IntegrationType.MAGAZORD} value={IntegrationType.MAGAZORD}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <MagazordBadge shouldHideName isMagazordClient /> Magazord
                </Stack>
              </MenuItem>
            </RHFSelect>

            <ContentForm type={type} />

            <RHFSwitch name="active" label="Integração ativa" />
          </Stack>
        </DialogContent>

        <DialogActions>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              onClose();
            }}
          >
            Cancelar
          </Button>

          <LoadingButton type="submit" variant="contained" loading={isSubmitting || isPending}>
            Adicionar
          </LoadingButton>
        </DialogActions>
      </FormProvider>
    </Dialog>
  );
}

type ContentFormProps = {
  type?: IntegrationType;
};

function ContentForm({ type }: ContentFormProps) {
  if (!type) return;

  if (type === IntegrationType.MAGAZORD) {
    // eslint-disable-next-line consistent-return
    return (
      <Stack spacing={3}>
        <RHFTextField name="content.url" label="Url" fullWidth />

        <RHFTextField name="content.token" label="Token" fullWidth />

        <RHFTextField name="content.password" label="Password" fullWidth />
      </Stack>
    );
  }

  // if (type === IntegrationType.GOALFY) {
  //   return (
  //     <Stack spacing={3}>
  //       <RHFTextField name="content.url" label="Url" fullWidth />

  //       <RHFTextField name="content.token" label="Token" fullWidth />
  //     </Stack>
  //   );
  // }

  // if (type === IntegrationType.MASTER) {
  //   return (
  //     <Stack spacing={3}>
  //       <RHFTextField name="content.url" label="Url" fullWidth />

  //       <RHFTextField name="content.token" label="Token" fullWidth />
  //     </Stack>
  //   );
  // }
}
