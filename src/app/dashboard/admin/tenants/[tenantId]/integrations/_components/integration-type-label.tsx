// @mui
import Tooltip from '@mui/material/Tooltip';

import MagazordIcon from 'src/assets/icons/magazord-icon';

import Label from 'src/components/label';

import { IntegrationType } from 'src/types/integration';

type Props = {
  type: IntegrationType;
};

export default function IntegrationTypeLabel({ type }: Props) {
  if (!type) return null;

  switch (type) {
    case IntegrationType.MAGAZORD:
      return (
        <Tooltip arrow placement="top" title="Integração Magazord">
          <Label variant="soft" startIcon={<MagazordIcon />}>
            Magazord
          </Label>
        </Tooltip>
      );

    default:
      return null;
  }
}
