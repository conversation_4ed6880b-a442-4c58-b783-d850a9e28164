import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import { Stack } from '@mui/system';
import { LoadingButton } from '@mui/lab';
import { Button, Dialog, DialogTitle, DialogActions, DialogContent } from '@mui/material';

import { EditIntegrationSchema } from 'src/schemas/admin/tenant/integration';
import { useIntegrationUpdateMutation } from 'src/mutations/tenant/integration-mutation';

import FormProvider from 'src/components/hook-form/form-provider';
import { RHFSwitch, RHFTextField } from 'src/components/hook-form';

import { IIntegration, IntegrationType } from 'src/types/integration';

type Props = {
  open: boolean;
  onClose: VoidFunction;
  tenantId: string;
  row: IIntegration;
};

type FormDataProps = {
  name: string;
  active: boolean;
  content: { [key: string]: string };
  slug: string;
};

export function IntegrationEditModal({ onClose, open, tenantId, row }: Props) {
  const defaultValues = {
    active: row.active,
    name: row.name,
    content: row.content as { [key: string]: string },
    slug: row.slug,
  };

  const methods = useForm<FormDataProps>({
    resolver: yupResolver(EditIntegrationSchema(row.type)),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const { mutateAsync, isPending } = useIntegrationUpdateMutation({
    callbackFn: () => {
      reset();
      onClose();
    },
  });

  const onSubmit = handleSubmit(async (updatedIntegration) => {
    mutateAsync({ updatedIntegration, integrationId: row.id });
  });

  return (
    <Dialog
      fullWidth
      maxWidth={false}
      open={open}
      onClose={() => {
        reset();
        onClose();
      }}
      PaperProps={{
        sx: { maxWidth: 720 },
      }}
    >
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <DialogTitle>Editar Integração {row.name}</DialogTitle>

        <DialogContent>
          <Stack spacing={3} sx={{ py: 2 }}>
            <RHFTextField name="name" label="Nome" fullWidth />

            <RHFTextField name="slug" label="Slug" fullWidth />

            <ContentForm type={row.type} />

            <RHFSwitch name="active" label="Integração ativa" />
          </Stack>
        </DialogContent>

        <DialogActions>
          <Button
            variant="outlined"
            onClick={() => {
              reset();
              onClose();
            }}
          >
            Cancelar
          </Button>

          <LoadingButton type="submit" variant="contained" loading={isSubmitting || isPending}>
            Salvar Alterações
          </LoadingButton>
        </DialogActions>
      </FormProvider>
    </Dialog>
  );
}

type ContentFormProps = {
  type?: IntegrationType;
};

function ContentForm({ type }: ContentFormProps) {
  if (!type) return;

  if (type === IntegrationType.MAGAZORD) {
    // eslint-disable-next-line consistent-return
    return (
      <Stack spacing={3}>
        <RHFTextField name="content.url" label="URL" fullWidth />

        <RHFTextField name="content.token" label="Token" fullWidth />

        <RHFTextField name="content.password" label="Password" fullWidth />
      </Stack>
    );
  }

  // if (type === IntegrationType.GOALFY) {
  //   return (
  //     <Stack spacing={3}>
  //       <RHFTextField name="content.url" label="URL" fullWidth />

  //       <RHFTextField name="content.token" label="Token" fullWidth />
  //     </Stack>
  //   );
  // }

  // if (type === IntegrationType.MASTER) {
  //   return (
  //     <Stack spacing={3}>
  //       <RHFTextField name="content.url" label="URL" fullWidth />

  //       <RHFTextField name="content.token" label="Token" fullWidth />
  //     </Stack>
  //   );
  // }
}
