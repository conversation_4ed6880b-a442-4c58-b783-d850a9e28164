import MagazordIcon from 'src/assets/icons/magazord-icon';

import Label from 'src/components/label';

export function MagazordBadge({
  isMagazordClient = false,
  shouldHideName = false,
}: {
  isMagazordClient?: boolean;
  shouldHideName?: boolean;
}) {
  return isMagazordClient ? (
    <Label
      variant="soft"
      color="success"
      startIcon={<MagazordIcon />}
      style={{
        width: shouldHideName ? 30 : 'auto',
        paddingRight: shouldHideName ? 0 : 'none',
      }}
    >
      {shouldHideName ? '' : 'Cliente Magazord'}
    </Label>
  ) : null;
}

// export function GoalfyBadge({
//   isGoalfyClient = false,
//   shouldHideName = false,
// }: {
//   isGoalfyClient?: boolean;
//   shouldHideName?: boolean;
// }) {
//   return isGoalfyClient ? (
//     <Label
//       variant="soft"
//       color="success"
//       startIcon={<GoalfyIcon />}
//       style={{
//         width: shouldHideName ? 30 : 'auto',
//         paddingRight: shouldHideName ? 0 : 'none',
//       }}
//     >
//       {shouldHideName ? '' : 'Cliente Goalfy'}
//     </Label>
//   ) : null;
// }

// export function ERPMasterBadge({
//   isMasterClient = false,
//   shouldHideName = false,
// }: {
//   isMasterClient?: boolean;
//   shouldHideName?: boolean;
// }) {
//   return isMasterClient ? (
//     <Label
//       variant="soft"
//       color="secondary"
//       startIcon={<ErpMasterIcon />}
//       style={{
//         width: shouldHideName ? 30 : 'auto',
//         paddingRight: shouldHideName ? 0 : 'none',
//       }}
//     >
//       {shouldHideName ? '' : 'Cliente Master'}
//     </Label>
//   ) : null;
// }
