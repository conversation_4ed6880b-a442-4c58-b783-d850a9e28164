import JsonView from '@uiw/react-json-view';
import { useParams } from 'next/navigation';
import { nordTheme } from '@uiw/react-json-view/nord';
import { lightTheme } from '@uiw/react-json-view/light';

import { Button, Tooltip, TableRow, useTheme, TableCell, IconButton } from '@mui/material';

import { paths } from 'src/routes/paths';

import { useBoolean } from 'src/hooks/use-boolean';

import Label from 'src/components/label';
import Iconify from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';

import { ITenant } from 'src/types/tenant';
import { IIntegration } from 'src/types/integration';

import IntegrationTypeLabel from './integration-type-label';
import { IntegrationEditModal } from './integration-edit-modal';

type Props = {
  row: IIntegration;
  tenant: ITenant;
  onDeleteRow: VoidFunction;
};

export function IntegrationTableRow({ row, tenant, onDeleteRow }: Props) {
  const { id, name, type, active, content, slug } = row;

  const edit = useBoolean();
  const confirm = useBoolean();

  const { tenantId } = useParams<{ tenantId: string }>();

  const theme = useTheme();

  return (
    <>
      <TableRow>
        <TableCell sx={{ whiteSpace: 'nowrap' }}>#{id}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{name}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>{slug}</TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>
          <IntegrationTypeLabel type={type} />
        </TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap', maxWidth: 250, minWidth: 250 }}>
          <JsonView
            value={content}
            collapsed={false}
            style={theme.palette.mode === 'dark' ? nordTheme : lightTheme}
          />
        </TableCell>

        <TableCell sx={{ whiteSpace: 'nowrap' }}>
          {active ? (
            <Label alignSelf="center" color="success">
              Ativo
            </Label>
          ) : (
            <Label alignSelf="center" color="warning">
              Inativo
            </Label>
          )}
        </TableCell>

        <TableCell align="right" sx={{ px: 1, whiteSpace: 'nowrap' }}>
          <Tooltip title="Configurações" placement="top" arrow>
            <IconButton
              href={paths.dashboard.admin.tenant.integrations.magazord.config(tenantId, row.id)}
            >
              <Iconify icon="solar:settings-bold-duotone" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Editar Integração" placement="top" arrow>
            <IconButton color="default" onClick={edit.onTrue}>
              <Iconify icon="solar:pen-bold" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Excluir Integração" placement="top" arrow>
            <IconButton color="error" onClick={confirm.onTrue}>
              <Iconify icon="solar:trash-bin-trash-bold" />
            </IconButton>
          </Tooltip>
        </TableCell>
      </TableRow>

      <ConfirmDialog
        open={confirm.value}
        onClose={confirm.onFalse}
        title="Excluir"
        content="Tem certeza que deseja excluir esta empresa? Essa ação não poderá ser desfeita."
        action={
          <Button variant="contained" color="error" onClick={onDeleteRow}>
            Excluir
          </Button>
        }
      />
      {edit.value && (
        <IntegrationEditModal
          tenantId={tenant.id}
          onClose={edit.onFalse}
          open={edit.value}
          row={row}
        />
      )}
    </>
  );
}
