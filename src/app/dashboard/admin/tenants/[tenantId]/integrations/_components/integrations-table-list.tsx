import { isEqual } from 'lodash';
import { useState, useEffect, useCallback } from 'react';

import { Card, Table, TableBody, TableContainer } from '@mui/material';

import { useIntegrationListQuery } from 'src/queries/tenant/integration-query';
import { useIntegrationDeleteMutation } from 'src/mutations/tenant/integration-mutation';

import Scrollbar from 'src/components/scrollbar';
import {
  useTable,
  TableNoData,
  getComparator,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

import { ITenant } from 'src/types/tenant';
import { IIntegration, IIntegrationTableFilterValue } from 'src/types/integration';

import { IntegrationTableRow } from './integration-table-row';
import IntegrationTableToolbar from './integration-table-toolbar';
import IntegrationTableFiltersResult from './integration-table-filters-result';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: 'id', label: 'Código' },
  { id: 'name', label: 'Nome' },
  { id: 'slug', label: 'Slug' },
  { id: 'type', label: 'Integração' },
  { id: 'content', label: 'Conteúdo' },
  { id: 'active', label: 'Ativo' },
  { id: '', width: 88 },
];

const defaultFilters = {
  name: '',
};

// ----------------------------------------------------------------------

type Props = {
  tenant: ITenant;
};

export function IntegrationsTable({ tenant }: Props) {
  const table = useTable();

  const { data: integrations = [], isFetching, isRefetching, refetch } = useIntegrationListQuery();

  const { mutateAsync } = useIntegrationDeleteMutation();

  const handleDeleteIntegration = async (integrationId: string) => {
    mutateAsync({ integrationId });
  };

  const [tableData, setTableData] = useState<IIntegration[]>([]);

  const handleFilters = useCallback(
    (name: string, value: IIntegrationTableFilterValue) => {
      table.onResetPage();
      setFilters((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    },
    [table]
  );

  const [filters, setFilters] = useState(defaultFilters);

  const canReset = !isEqual(defaultFilters, filters);

  const dataFiltered = applyFilter({
    inputData: tableData,
    comparator: getComparator(table.order, table.orderBy),
    filters,
  });

  const handleResetFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);

  useEffect(() => {
    setTableData(integrations || []);
  }, [integrations]);

  useEffect(() => {
    refetch();
  }, [refetch, table.page, table.rowsPerPage]);

  const notFound = !isFetching && !isRefetching && !tableData.length;

  return (
    <Card>
      <IntegrationTableToolbar filters={filters} onFilters={handleFilters} />

      {canReset && (
        <IntegrationTableFiltersResult
          filters={filters}
          onFilters={handleFilters}
          //
          onResetFilters={handleResetFilters}
          //
          results={dataFiltered.length}
          sx={{ p: 2.5, pt: 0 }}
        />
      )}
      <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
        <Scrollbar>
          <Table size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
            <TableHeadCustom
              order={table.order}
              orderBy={table.orderBy}
              headLabel={TABLE_HEAD}
              onSort={table.onSort}
            />

            <TableBody>
              {dataFiltered
                ?.slice(
                  table.page * table.rowsPerPage,
                  table.page * table.rowsPerPage + table.rowsPerPage
                )
                .map((row) => (
                  <IntegrationTableRow
                    row={row}
                    tenant={tenant}
                    key={row.id}
                    onDeleteRow={() => handleDeleteIntegration(row.id)}
                  />
                ))}

              <TableNoData notFound={notFound} />
            </TableBody>
          </Table>
        </Scrollbar>
      </TableContainer>

      <TablePaginationCustom
        totalPages={Math.floor(tableData.length / table.rowsPerPage)}
        count={integrations.length}
        page={table.page}
        rowsPerPage={table.rowsPerPage}
        onPageChange={table.onChangePage}
        onRowsPerPageChange={table.onChangeRowsPerPage}
        //
        dense={table.dense}
        onChangeDense={table.onChangeDense}
      />
    </Card>
  );
}

function applyFilter({
  inputData,
  comparator,
  filters,
}: {
  inputData: IIntegration[];
  comparator: (a: any, b: any) => number;
  filters: typeof defaultFilters;
}) {
  const { name } = filters;

  const stabilizedThis = inputData?.map((el, index) => [el, index] as const);

  stabilizedThis?.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });

  inputData = stabilizedThis.map((el) => el[0]);

  if (name) {
    inputData = inputData.filter(
      (company) => company.name.toLowerCase().indexOf(name.toLowerCase()) !== -1
    );
  }

  return inputData;
}
