import { PropsWithChildren } from 'react';

import { AccountRole } from 'src/enum/account';
import { RoleBasedGuard } from 'src/auth/guard';

// ----------------------------------------------------------------------

type Props = PropsWithChildren;

export default function RootLayout({ children }: Props) {
  return (
    // TODO: REVIEW ROLES INSIDE THESE ROLE BASED GUARD
    <RoleBasedGuard hasContent isSuper roles={[AccountRole.ADMIN]}>
      {children}
    </RoleBasedGuard>
  );
}
