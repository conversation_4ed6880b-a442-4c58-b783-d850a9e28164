'use client';

import { Refine } from '@refinedev/core';

import { AuthGuard } from 'src/auth/guard';
import DashboardLayout from 'src/layouts/dashboard';
import { CompanyProvider } from 'src/company/context';

// ----------------------------------------------------------------------

type Props = {
  children: React.ReactNode;
};

export default function Layout({ children }: Props) {
  return (
    <Refine>
      <AuthGuard>
        <CompanyProvider>
          <DashboardLayout>{children}</DashboardLayout>
        </CompanyProvider>
      </AuthGuard>
    </Refine>
  );
}
