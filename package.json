{"name": "@minimal-kit/next-ts", "author": "Minimals", "version": "5.7.0", "description": "Next & TypeScript", "private": true, "scripts": {"dev": "next dev -p 8082", "start": "next start -p 8082", "build": "next build", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "rm:all": "rm -rf node_modules .next out dist build", "re:start": "yarn rm:all && yarn install && yarn dev", "re:build": "yarn rm:all && yarn install && yarn build", "re:build-npm": "npm run rm:all && npm install && npm run build", "dev:ts": "yarn dev & yarn ts:watch", "ts": "tsc --noEmit --incremental", "ts:watch": "yarn ts --watch"}, "dependencies": {"@auth0/auth0-react": "^2.2.3", "@emotion/cache": "^11.10.5", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hello-pangea/dnd": "^16.5.0", "@hookform/resolvers": "^3.3.2", "@iconify/react": "^4.1.1", "@mui/icons-material": "^5.16.6", "@mui/lab": "^5.0.0-alpha.155", "@mui/material": "^5.14.20", "@mui/system": "^5.14.20", "@mui/x-data-grid": "^6.18.4", "@mui/x-date-pickers": "^6.18.4", "@react-pdf/renderer": "^3.1.14", "@refinedev/core": "^4.51.0", "@refinedev/mui": "^5.17.0", "@supabase/supabase-js": "^2.39.0", "@tanstack/react-query": "^5.29.2", "@tanstack/react-query-devtools": "^5.32.0", "@uiw/react-json-view": "^2.0.0-alpha.30", "apexcharts": "^3.44.2", "autosuggest-highlight": "^3.3.4", "aws-amplify": "^6.0.6", "axios": "^1.6.2", "date-fns": "^2.30.0", "firebase": "^10.7.1", "framer-motion": "^10.16.16", "highlight.js": "^11.9.0", "i18next": "^23.7.8", "i18next-browser-languagedetector": "^7.2.0", "lodash": "^4.17.21", "mapbox-gl": "^3.0.1", "mui-one-time-password-input": "^2.0.1", "next": "^14.0.4", "notistack": "^3.0.1", "nprogress": "^0.2.0", "numeral": "^2.0.6", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-barcode": "^1.5.3", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-i18next": "^13.5.0", "react-imask": "^7.6.0", "react-joyride": "^2.7.1", "react-lazy-load-image-component": "^1.6.0", "react-map-gl": "^7.1.6", "react-markdown": "^9.0.1", "react-organizational-chart": "^2.2.1", "react-quill": "^2.0.0", "react-slick": "^0.29.0", "rehype-highlight": "^7.0.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "simplebar-react": "^3.2.4", "slick-carousel": "^1.8.1", "stylis": "^4.3.0", "stylis-plugin-rtl": "^2.1.1", "swr": "^2.2.4", "yet-another-react-lightbox": "^3.15.6", "yup": "^1.3.2"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@tanstack/eslint-plugin-query": "^5.28.11", "@types/autosuggest-highlight": "^3.2.3", "@types/lodash": "^4.14.202", "@types/mapbox-gl": "^2.7.19", "@types/node": "^20.10.4", "@types/nprogress": "^0.2.3", "@types/react": "^18.2.43", "@types/react-lazy-load-image-component": "^1.6.3", "@types/react-slick": "^0.23.12", "@types/stylis": "^4.2.4", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-perfectionist": "^2.5.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^3.0.0", "prettier": "^3.1.0", "typescript": "^5.3.3"}}